package com.myckasp.celle.commands;

import com.myckasp.celle.Celle;
import com.myckasp.celle.listeners.AdminChatListener;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.models.Cell;
import com.myckasp.celle.models.CellGroup;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class AdminCommand implements CommandExecutor, Listener {
    
    private final Celle plugin;
    private final CellManager cellManager;
    
    public AdminCommand(Celle plugin) {
        this.plugin = plugin;
        this.cellManager = plugin.getCellManager();
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Denne kommando kan kun bruges af spillere!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (!player.hasPermission("ce.admin") && !player.hasPermission("celle.admin") && !player.hasPermission("celle.*")) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
            return true;
        }
        
        if (args.length == 0) {
            showGroupList(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "sign":
                handleAvailableCellsSign(player);
                break;
            case "update":
                handleUpdateCheck(player);
                break;
            default:
                showGroupList(player);
                break;
        }
        
        return true;
    }
    
    public void showGroupList(Player player) {
        List<CellGroup> groups = cellManager.getAllGroups();
        
        // Create a 5-row inventory (45 slots)
        Inventory inventory = Bukkit.createInventory(null, 45, "§6Grupper Administration");
        
        // Add group items
        for (CellGroup group : groups) {
            ItemStack item = createGroupItem(group);
            inventory.addItem(item);
        }
        
        // Add gray glass to the bottom row
        ItemStack grayGlass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7);
        ItemMeta glassMeta = grayGlass.getItemMeta();
        if (glassMeta != null) {
            glassMeta.setDisplayName(" ");
            grayGlass.setItemMeta(glassMeta);
        }
        
        // Fill bottom row with gray glass (slots 36-44)
        for (int i = 36; i < 45; i++) {
            if (i != 40) { // Skip slot 40 (5th slot in bottom row)
                inventory.setItem(i, grayGlass);
            }
        }
        
        // Add lime dye in slot 40 (5th slot in bottom row) for creating new groups
        ItemStack limeDye = new ItemStack(Material.INK_SACK, 1, (short) 10);
        ItemMeta limeMeta = limeDye.getItemMeta();
        if (limeMeta != null) {
            limeMeta.setDisplayName("§aOpret Ny Gruppe");
            List<String> lore = new ArrayList<>();
            lore.add("§7Klik for at oprette en ny gruppe");
            limeMeta.setLore(lore);
            limeDye.setItemMeta(limeMeta);
        }
        inventory.setItem(40, limeDye);
        
        player.openInventory(inventory);
    }
    
    private ItemStack createGroupItem(CellGroup group) {
        ItemStack item = new ItemStack(Material.PAPER);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName("§e" + group.getName());
            
            List<String> lore = new ArrayList<>();
            lore.add("§7Beskrivelse: " + group.getDescription());
            lore.add("§7Standard lejepris: §a$" + (int) group.getDefaultRentPrice() + "/dag");
            lore.add("§7Maks lejeperiode: §b" + group.getMaxRentDays() + " dage");
            lore.add("§7Maks medlemmer: §b" + group.getMaxMembers());
            
            // Count cells in this group
            int cellCount = cellManager.getCellsByGroup(group.getName()).size();
            lore.add("§7Antal celler: §b" + cellCount);
            
            // Check if this is the default group
            if ("default".equalsIgnoreCase(group.getName())) {
                lore.add("§cStandard gruppe - Kan ikke slettes");
            }
            
            // Add available cells sign option
            lore.add("§aVenstreklik for at administrere gruppen");
            lore.add("§aHøjreklik for at få tilgængelige celler skilt");
            
            meta.setLore(lore);
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    private void handleAvailableCellsSign(Player player) {
        handleAvailableCellsSign(player, null);
    }
    
    private void handleAvailableCellsSign(Player player, String groupName) {
        // Give player a sign item
        ItemStack signItem = new ItemStack(Material.SIGN);
        ItemMeta meta = signItem.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName("§eTilgængelige Celler Skilt");
            List<String> lore = new ArrayList<>();
            lore.add("§7Placer dette skilt for at oprette");
            lore.add("§7en liste over tilgængelige celler");
            
            if (groupName != null) {
                lore.add("§7for gruppen: §e" + groupName);
            } else {
                lore.add("§7for den gruppe du kigger på.");
            }
            
            meta.setLore(lore);
            signItem.setItemMeta(meta);
        }
        
        player.getInventory().addItem(signItem);
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.sign-given"));
        
        if (groupName != null) {
            player.sendMessage("§7Placer skiltet hvor du vil have listen for gruppen: §e" + groupName);
        } else {
            player.sendMessage("§7Placer skiltet hvor du vil have listen.");
        }
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Check if this is our admin inventory
        if (event.getView().getTitle().equals("§6Grupper Administration")) {
            event.setCancelled(true);
            
            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || clickedItem.getType() == Material.AIR) {
                return;
            }
            
            // Handle lime dye click (create new group)
            if (clickedItem.getType() == Material.INK_SACK && clickedItem.getDurability() == 10) {
                handleCreateNewGroup(player);
                // Close the GUI to prevent interaction while waiting for chat input
                player.closeInventory();
                return;
            }
            
            // Get group name from display name
            String groupName = clickedItem.getItemMeta().getDisplayName().replace("§e", "");
            
            // Handle different click types
            ClickType clickType = event.getClick();
            
            if (clickType.isLeftClick()) {
                showGroupManagement(player, groupName);
            } else if (clickType.isRightClick()) {
                // Right click gives available cells sign
                handleAvailableCellsSign(player, groupName);
            }
        }
        
        // Check if this is a group management inventory
        if (event.getView().getTitle().startsWith("§6Administration: ")) {
            event.setCancelled(true);
            
            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || clickedItem.getType() == Material.AIR) {
                return;
            }
            
            String groupName = event.getView().getTitle().replace("§6Administration: ", "");
            
            // Handle management menu clicks
            if (clickedItem.getType() == Material.WATCH) {
                // Max renting time
                if (event.getClick().isRightClick()) {
                    // Reset to default
                    resetMaxRentDays(player, groupName);
                } else {
                    handleMaxRentingTime(player, groupName);
                }
            } else if (clickedItem.getType() == Material.SKULL_ITEM) {
                // Max members
                if (event.getClick().isRightClick()) {
                    // Reset to default
                    resetMaxMembers(player, groupName);
                } else {
                    handleMaxMembers(player, groupName);
                }
            } else if (clickedItem.getType() == Material.EMERALD) {
                // Price
                if (event.getClick().isRightClick()) {
                    // Reset to default
                    resetPrice(player, groupName);
                } else {
                    handlePrice(player, groupName);
                }
            } else if (clickedItem.getType() == Material.BARRIER) {
                // Remove group (only if not default)
                if (!"default".equalsIgnoreCase(groupName)) {
                    if (clickedItem.getItemMeta().getDisplayName().equals("§cSlet Gruppe")) {
                        handleRemoveGroup(player, groupName);
                    } else {
                        showGroupManagement(player, groupName);
                    }
                } else {
                    player.sendMessage("§cDu kan ikke slette standardgruppen!");
                }
            } else if (clickedItem.getType() == Material.ARROW) {
                // Back button
                showGroupList(player);
            }
        }
    }
    
    public void showGroupManagement(Player player, String groupName) {
        CellGroup group = cellManager.getGroup(groupName);
        if (group == null) {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
            return;
        }
        
        Inventory inventory = Bukkit.createInventory(null, 27, "§6Administration: " + groupName);
        
        // Max renting time item
        ItemStack maxRentItem = new ItemStack(Material.WATCH);
        ItemMeta maxRentMeta = maxRentItem.getItemMeta();
        if (maxRentMeta != null) {
            maxRentMeta.setDisplayName("§eMaks Lejeperiode");
            List<String> lore = new ArrayList<>();
            lore.add("§7Nuværende: §b" + group.getMaxRentDays() + " dage");
            lore.add("§aVenstreklik for at ændre");
            lore.add("§cHøjreklik for at nulstille");
            maxRentMeta.setLore(lore);
            maxRentItem.setItemMeta(maxRentMeta);
        }
        inventory.setItem(10, maxRentItem);
        
        // Max members item
        ItemStack maxMembersItem = new ItemStack(Material.SKULL_ITEM);
        ItemMeta maxMembersMeta = maxMembersItem.getItemMeta();
        if (maxMembersMeta != null) {
            maxMembersMeta.setDisplayName("§eMaks Medlemmer");
            List<String> lore = new ArrayList<>();
            lore.add("§7Nuværende: §b" + group.getMaxMembers());
            lore.add("§aVenstreklik for at ændre");
            lore.add("§cHøjreklik for at nulstille");
            maxMembersMeta.setLore(lore);
            maxMembersItem.setItemMeta(maxMembersMeta);
        }
        inventory.setItem(12, maxMembersItem);
        
        // Price item
        ItemStack priceItem = new ItemStack(Material.EMERALD);
        ItemMeta priceMeta = priceItem.getItemMeta();
        if (priceMeta != null) {
            priceMeta.setDisplayName("§eLejepris");
            List<String> lore = new ArrayList<>();
            lore.add("§7Nuværende: §a$" + (int) group.getDefaultRentPrice() + "/dag");
            lore.add("§aVenstreklik for at ændre");
            lore.add("§cHøjreklik for at nulstille");
            priceMeta.setLore(lore);
            priceItem.setItemMeta(priceMeta);
        }
        inventory.setItem(14, priceItem);
        
        // Remove group item (only if not default)
        ItemStack removeItem = new ItemStack(Material.BARRIER);
        ItemMeta removeMeta = removeItem.getItemMeta();
        if (removeMeta != null) {
            if (!"default".equalsIgnoreCase(groupName)) {
                removeMeta.setDisplayName("§cSlet Gruppe");
                List<String> lore = new ArrayList<>();
                lore.add("§7Slet gruppen permanent");
                lore.add("§cDette kan ikke fortrydes!");
                removeMeta.setLore(lore);
            } else {
                removeMeta.setDisplayName("§cStandard Gruppe");
                List<String> lore = new ArrayList<>();
                lore.add("§cStandard gruppen kan ikke slettes");
                removeMeta.setLore(lore);
            }
            removeItem.setItemMeta(removeMeta);
        }
        inventory.setItem(16, removeItem);
        
        // Back button
        ItemStack backItem = new ItemStack(Material.ARROW);
        ItemMeta backMeta = backItem.getItemMeta();
        if (backMeta != null) {
            backMeta.setDisplayName("§7Tilbage");
            backItem.setItemMeta(backMeta);
        }
        inventory.setItem(26, backItem);
        
        player.openInventory(inventory);
    }
    
    private void showGroupInfo(Player player, String groupName) {
        CellGroup group = cellManager.getGroup(groupName);
        if (group == null) {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
            return;
        }
        
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.group-info-title"));
        player.sendMessage("§7Navn: §e" + group.getName());
        player.sendMessage("§7Beskrivelse: §e" + group.getDescription());
        player.sendMessage("§7Standard lejepris: §a$" + (int) group.getDefaultRentPrice() + "/dag");
        player.sendMessage("§7Maks lejeperiode: §b" + group.getMaxRentDays() + " dage");
        player.sendMessage("§7Maks medlemmer: §b" + group.getMaxMembers());
        player.sendMessage("§7Antal celler: §b" + cellManager.getCellsByGroup(groupName).size());
        
        if ("default".equalsIgnoreCase(groupName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("admin.default-group-protected"));
        }
    }
    
    // These methods are no longer needed as we get values directly from CellGroup
    // private int getMaxRentDaysForGroup(String groupName) {
    //     return cellManager.getGroup(groupName).getMaxRentDays();
    // }
    //
    // private int getMaxMembersForGroup(String groupName) {
    //     return cellManager.getGroup(groupName).getMaxMembers();
    // }
    
    private void handleMaxRentingTime(Player player, String groupName) {
        // Get the chat listener and request input
        AdminChatListener chatListener = plugin.getAdminChatListener();
            
        if (chatListener != null) {
            chatListener.requestMaxRentDaysInput(player, groupName);
        } else {
            player.sendMessage("§cFejl: Chat listener er ikke tilgængelig!");
        }
    }
    
    private void handleMaxMembers(Player player, String groupName) {
        // Get the chat listener and request input
        AdminChatListener chatListener = plugin.getAdminChatListener();
            
        if (chatListener != null) {
            chatListener.requestMaxMembersInput(player, groupName);
        } else {
            player.sendMessage("§cFejl: Chat listener er ikke tilgængelig!");
        }
    }
    
    private void resetMaxRentDays(Player player, String groupName) {
        CellGroup group = cellManager.getGroup(groupName);
        if (group != null) {
            // Reset to default value (10 days)
            int defaultMaxRentDays = 10;
            group.setMaxRentDays(defaultMaxRentDays);
            cellManager.saveGroup(group);
            player.sendMessage("§aMaks lejeperiode for gruppen '" + groupName + "' er blevet nulstillet til " + defaultMaxRentDays + " dage.");
            // Reopen the group management menu with updated values
            showGroupManagement(player, groupName);
        } else {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
        }
    }
    
    private void resetMaxMembers(Player player, String groupName) {
        CellGroup group = cellManager.getGroup(groupName);
        if (group != null) {
            // Reset to default value (1 member)
            int defaultMaxMembers = 1;
            group.setMaxMembers(defaultMaxMembers);
            cellManager.saveGroup(group);
            player.sendMessage("§aMaks antal medlemmer for gruppen '" + groupName + "' er blevet nulstillet til " + defaultMaxMembers + ".");
            // Reopen the group management menu with updated values
            showGroupManagement(player, groupName);
        } else {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
        }
    }
    
    private void resetPrice(Player player, String groupName) {
        CellGroup group = cellManager.getGroup(groupName);
        if (group != null) {
            // Reset to default value (350)
            double defaultPrice = 350.0;
            group.setDefaultRentPrice(defaultPrice);
            cellManager.saveGroup(group);
            player.sendMessage("§aLejepris for gruppen '" + groupName + "' er blevet nulstillet til $" + (int) defaultPrice + "/dag.");
            // Reopen the group management menu with updated values
            showGroupManagement(player, groupName);
        } else {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
        }
    }
    
    private void handlePrice(Player player, String groupName) {
        // Get the chat listener and request input
        AdminChatListener chatListener = plugin.getAdminChatListener();
            
        if (chatListener != null) {
            chatListener.requestPriceInput(player, groupName);
        } else {
            player.sendMessage("§cFejl: Chat listener er ikke tilgængelig!");
        }
    }
    
    private void handleUpdateCheck(Player player) {
        if (!player.hasPermission("ce.admin") && !player.hasPermission("celle.admin") && !player.hasPermission("celle.*")) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
            return;
        }
        
        player.sendMessage("§eChecking for updates...");
        
        // Check for updates and notify the player when complete
        plugin.getUpdateManager().checkForUpdatesAndNotify(player);
    }
    
    private void handleRemoveGroup(Player player, String groupName) {
        // Get the chat listener and request confirmation
        AdminChatListener chatListener = plugin.getAdminChatListener();
            
        if (chatListener != null) {
            chatListener.requestRemoveGroupConfirm(player, groupName);
        } else {
            player.sendMessage("§cFejl: Chat listener er ikke tilgængelig!");
        }
    }
    
    private void handleCreateNewGroup(Player player) {
        // Get the chat listener and request input for new group name
        AdminChatListener chatListener = plugin.getAdminChatListener();
            
        if (chatListener != null) {
            chatListener.requestNewGroupNameInput(player);
        } else {
            player.sendMessage("§cFejl: Chat listener er ikke tilgængelig!");
        }
    }
}