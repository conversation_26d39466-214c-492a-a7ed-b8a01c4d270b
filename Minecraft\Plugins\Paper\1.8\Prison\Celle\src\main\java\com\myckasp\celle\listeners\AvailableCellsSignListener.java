package com.myckasp.celle.listeners;

import com.myckasp.celle.Celle;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.models.Cell;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.Sign;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AvailableCellsSignListener implements Listener {
    
    private final Celle plugin;
    private final CellManager cellManager;
    private final Map<Location, List<Location>> placedSigns = new HashMap<>();
    private final Map<Location, String> signGroups = new ConcurrentHashMap<>();
    private final Map<Location, Long> lastUpdateTime = new ConcurrentHashMap<>();
    
    public AvailableCellsSignListener(Celle plugin) {
        this.plugin = plugin;
        this.cellManager = plugin.getCellManager();
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        
        // Start the sign refresh task
        startSignRefreshTask();
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        Block block = event.getClickedBlock();
        ItemStack item = event.getItem();
        
        // Check if player is clicking a block with the special sign item
        if (event.getAction() == Action.RIGHT_CLICK_BLOCK && item != null &&
            item.getType() == Material.SIGN &&
            item.hasItemMeta() &&
            item.getItemMeta().hasDisplayName() &&
            item.getItemMeta().getDisplayName().equals("§eTilgængelige Celler Skilt")) {

            event.setCancelled(true);
            
            // Get the group name from the player's location or inventory
            // For now, we'll use a default group or prompt the player
            String groupName = getDefaultGroupName(player);
            
            if (groupName == null) {
                player.sendMessage("§cKunne ikke bestemme gruppe for dette skilt!");
                return;
            }
            
            // Get all available cells for this group
            List<Cell> availableCells = cellManager.getAvailableCellsByGroup(groupName);

            if (availableCells.isEmpty()) {
                player.sendMessage("§cIngen celler i denne gruppe!");
                return;
            }

            // Remove the sign item from player's inventory
            item.setAmount(item.getAmount() - 1);

            // Create cells signs immediately in 3x3 grid around clicked location
            createAvailableCellsSigns(block, groupName, availableCells, player);

            player.sendMessage(plugin.getLanguageManager().getMessage("admin.sign-placed"));
        }
        
        // Check if player is clicking an existing available cells sign
        if (event.getAction() == Action.RIGHT_CLICK_BLOCK && block != null && 
            block.getState() instanceof Sign) {
            
            Sign sign = (Sign) block.getState();
            
            // Check if this is our special sign
            if (sign.getLine(0).equals("§e§lTILGÆNGELIGE") && 
                sign.getLine(1).equals("§e§lCELLER")) {
                
                event.setCancelled(true);
                player.sendMessage(plugin.getLanguageManager().getMessage("admin.sign-info", sign.getLine(2)));
            }
        }
    }
    
    private String getDefaultGroupName(Player player) {
        // For now, return the default group
        // In a more complex implementation, this could determine the group
        // based on the player's location, permissions, or other factors
        return "default";
    }
    
    private void createAvailableCellsSign(Block block, String groupName) {
        // Determine the wall direction for the main sign
        BlockFace wallFacing = getWallFacingDirection(block);

        // Set the block to a wall sign with proper facing
        block.setType(Material.WALL_SIGN);

        // Set the sign facing direction
        byte data = 0;
        switch (wallFacing) {
            case NORTH: data = 2; break; // North
            case SOUTH: data = 3; break; // South
            case WEST:  data = 4; break; // West
            case EAST:  data = 5; break; // East
        }
        block.setData(data);

        if (block.getState() instanceof Sign) {
            Sign sign = (Sign) block.getState();

            // Set the sign text
            sign.setLine(0, "§e§lTILGÆNGELIGE");
            sign.setLine(1, "§e§lCELLER");
            sign.setLine(2, "§f" + groupName);
            sign.setLine(3, "§aOpdaterer...");

            sign.update();

        }
    }
    
    // Removed the repeating task that was recreating signs every 5 minutes
    // Signs are now created once and don't need constant recreation
    

    private BlockFace getWallFacingDirection(Block block) {
        // Check adjacent blocks to determine which wall this sign should face
        // We want to find which direction has a solid block (the wall)
        BlockFace[] faces = {BlockFace.NORTH, BlockFace.SOUTH, BlockFace.EAST, BlockFace.WEST};

        for (BlockFace face : faces) {
            Block adjacent = block.getRelative(face);
            if (adjacent.getType().isSolid()) {
                // Return the opposite face so the sign faces away from the wall
                return face.getOppositeFace();
            }
        }

        // If no adjacent solid blocks found, try to determine from the clicked face
        // For now, default to NORTH if we can't determine
        return BlockFace.NORTH; // Default fallback
    }

    private BlockFace getPlayerFacingDirection(Player player) {
        // Get the player's yaw (rotation around Y axis)
        float yaw = player.getLocation().getYaw();

        // Normalize yaw to 0-360
        yaw = (yaw % 360 + 360) % 360;

        // Determine facing direction based on yaw
        if (yaw >= 315 || yaw < 45) {
            return BlockFace.SOUTH; // Facing positive Z (south)
        } else if (yaw >= 45 && yaw < 135) {
            return BlockFace.WEST; // Facing negative X (west)
        } else if (yaw >= 135 && yaw < 225) {
            return BlockFace.NORTH; // Facing negative Z (north)
        } else {
            return BlockFace.EAST; // Facing positive X (east)
        }
    }
    
    private void createAvailableCellsSigns(Block centerBlock, String groupName, List<Cell> cells, Player player) {
        // Clear any existing signs in the area first
        clearSigns(centerBlock);

        // Create new signs if there are cells
        if (!cells.isEmpty()) {
            // Create signs in a 3x3 grid pattern (3 rows, 3 columns) - always 9 signs
            int signCount = 9; // Always create 9 signs

            List<Location> signLocations = new ArrayList<>();

            // Define the 3x3 pattern around the center block
            // The center block is at (0,0), so we create a 3x3 grid around it
            // Each entry contains {xOffset, yOffset, zOffset}
            int[][] offsets = {
                {-1, 1, -1}, {0, 1, -1}, {1, 1, -1},  // Top row (north) - Y+1
                {-1, 0, -1}, {0, 0, -1}, {1, 0, -1},  // Middle row - Y+0
                {-1, -1, -1}, {0, -1, -1}, {1, -1, -1}   // Bottom row (south) - Y-1
            };

            // Determine the facing direction based on player's head direction
            BlockFace playerFacing = getPlayerFacingDirection(player);

            // Only show available cells starting from upper left
            int availableCellIndex = 0;
            int maxAvailableCells = cells.size();

            for (int i = 0; i < signCount; i++) {
                int xOffset = offsets[i][0];
                int yOffset = offsets[i][1];
                int zOffset = offsets[i][2];

                // Calculate position relative to the center block
                // Place signs in front of the wall
                double x, y, z;

                // Adjust coordinates based on player facing direction
                // Place signs in front of the wall, not on it
                switch (playerFacing) {
                    case NORTH:
                        x = centerBlock.getX() + xOffset;
                        y = centerBlock.getY() + yOffset;
                        z = centerBlock.getZ() + 1; // Place signs in front of north wall
                        break;
                    case SOUTH:
                        x = centerBlock.getX() + xOffset;
                        y = centerBlock.getY() + yOffset;
                        z = centerBlock.getZ() - 1; // Place signs in front of south wall
                        break;
                    case EAST:
                        x = centerBlock.getX() - 1; // Place signs in front of east wall
                        y = centerBlock.getY() + yOffset;
                        z = centerBlock.getZ() + xOffset; // Use xOffset for Z when facing east/west
                        break;
                    case WEST:
                        x = centerBlock.getX() + 1; // Place signs in front of west wall
                        y = centerBlock.getY() + yOffset;
                        z = centerBlock.getZ() + xOffset; // Use xOffset for Z when facing east/west
                        break;
                    default:
                        x = centerBlock.getX() + xOffset;
                        y = centerBlock.getY() + yOffset;
                        z = centerBlock.getZ() + zOffset;
                        break;
                }

                // Create sign location
                Location signLoc = new Location(centerBlock.getWorld(), x, y, z);

                // Get the cell for this sign (only show available cells starting from upper left)
                Cell cellForSign = null;
                if (availableCellIndex < maxAvailableCells) {
                    cellForSign = cells.get(availableCellIndex);
                    availableCellIndex++;
                }

                // Place the sign attached to the wall
                if (cellForSign != null) {
                    createSign(signLoc, cellForSign, playerFacing);
                } else {
                    createEmptySign(signLoc, playerFacing);
                }
                signLocations.add(signLoc);
            }

            // Store the locations of created signs
            placedSigns.put(centerBlock.getLocation(), signLocations);
            signGroups.put(centerBlock.getLocation(), groupName);
            lastUpdateTime.put(centerBlock.getLocation(), System.currentTimeMillis());
        }
    }
    
    private void clearSigns(Block signBlock) {
        // Remove existing signs in the 3x3 area
        Location center = signBlock.getLocation();

        // Get stored sign locations if available
        List<Location> signLocations = placedSigns.get(center);

        if (signLocations != null) {
            for (Location signLoc : signLocations) {
                Block signBlockToRemove = signLoc.getBlock();
                if (signBlockToRemove.getType() == Material.WALL_SIGN ||
                    signBlockToRemove.getType() == Material.SIGN_POST) {
                    signBlockToRemove.setType(Material.AIR);
                }
            }
            placedSigns.remove(center);
            signGroups.remove(center);
            lastUpdateTime.remove(center);
        } else {
            // If no stored locations, clear the entire 3x3 area including vertical positions
            // Use the same pattern as in createAvailableCellsSigns for consistency
            int[][] offsets = {
                {-1, 1, -1}, {0, 1, -1}, {1, 1, -1},  // Top row (north) - Y+1
                {-1, 0, -1}, {0, 0, -1}, {1, 0, -1},  // Middle row - Y+0
                {-1, -1, -1}, {0, -1, -1}, {1, -1, -1}   // Bottom row (south) - Y-1
            };

            for (int[] offset : offsets) {
                int xOffset = offset[0];
                int yOffset = offset[1];
                int zOffset = offset[2];

                Location signLoc = center.clone().add(xOffset, yOffset, zOffset);
                Block signBlockToRemove = signLoc.getBlock();
                if (signBlockToRemove.getType() == Material.WALL_SIGN ||
                    signBlockToRemove.getType() == Material.SIGN_POST) {
                    signBlockToRemove.setType(Material.AIR);
                }
            }
        }
    }
    

    private void createSign(Location location, Cell cell, BlockFace facing) {
        // Get the block at the location
        Block block = location.getBlock();

        // Set the block to a wall sign
        block.setType(Material.WALL_SIGN);

        // Set the sign facing direction using proper block data
        // Wall sign data: 2=faces south (attached north), 3=faces north (attached south), 4=faces east (attached west), 5=faces west (attached east)
        byte data = 2; // Default to south
        switch (facing) {
            case NORTH: data = 3; break; // Faces north
            case SOUTH: data = 2; break; // Faces south
            case WEST:  data = 5; break; // Faces west
            case EAST:  data = 4; break; // Faces east
        }
        block.setData(data);

        // Get the sign state and set the text
        if (block.getState() instanceof Sign) {
            Sign sign = (Sign) block.getState();

            // Set the sign text - display cell name on the first line only
            sign.setLine(0, "§e" + cell.getName());
            sign.setLine(1, "");
            sign.setLine(2, "");
            sign.setLine(3, "");

            // Update the sign
            sign.update();

        } else {
        }
    }

    private void createEmptySign(Location location, BlockFace facing) {
        // Get the block at the location
        Block block = location.getBlock();

        // Set the block to a wall sign
        block.setType(Material.WALL_SIGN);

        // Set the sign facing direction using proper block data
        // Wall sign data: 2=faces south (attached north), 3=faces north (attached south), 4=faces east (attached west), 5=faces west (attached east)
        byte data = 2; // Default to south
        switch (facing) {
            case NORTH: data = 3; break; // Faces north
            case SOUTH: data = 2; break; // Faces south
            case WEST:  data = 5; break; // Faces west
            case EAST:  data = 4; break; // Faces east
        }
        block.setData(data);

        // Get the sign state and set empty text
        if (block.getState() instanceof Sign) {
            Sign sign = (Sign) block.getState();

            // Set empty sign text
            sign.setLine(0, "");
            sign.setLine(1, "");
            sign.setLine(2, "");
            sign.setLine(3, "");

            // Update the sign
            sign.update();

        } else {
        }
    }
    
    /**
     * Calculates the position index (0-8) for a sign in the 3x3 grid
     * 0 1 2
     * 3 4 5
     * 6 7 8
     * @param location The location of the sign
     * @param centerLocation The center location of the grid
     * @return The position index (0-8)
     */
    private int calculateSignPositionIndex(Location location, Location centerLocation) {
        double deltaX = location.getX() - centerLocation.getX();
        double deltaZ = location.getZ() - centerLocation.getZ();
        
        // Determine position based on relative coordinates
        if (deltaZ > 0) { // North (upper row)
            if (deltaX < -0.5) return 0; // Upper left
            if (deltaX >= -0.5 && deltaX <= 0.5) return 1; // Upper middle
            if (deltaX > 0.5) return 2; // Upper right
        } else if (deltaZ >= -0.5 && deltaZ <= 0.5) { // Middle row
            if (deltaX < -0.5) return 3; // Middle left
            if (deltaX >= -0.5 && deltaX <= 0.5) return 4; // Center
            if (deltaX > 0.5) return 5; // Middle right
        } else if (deltaZ < 0) { // South (bottom row)
            if (deltaX < -0.5) return 6; // Bottom left
            if (deltaX >= -0.5 && deltaX <= 0.5) return 7; // Bottom middle
            if (deltaX > 0.5) return 8; // Bottom right
        }
        
        // Default fallback
        return 0;
    }
    
    /**
     * Starts the sign refresh task that runs every 5 minutes
     */
    private void startSignRefreshTask() {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            refreshAllSigns();
        }, 20L * 30, 20L * 60 * 5); // First run after 30 seconds, then every 5 minutes
    }
    
    /**
     * Refreshes all available cells signs
     */
    private void refreshAllSigns() {
        long currentTime = System.currentTimeMillis();
        
        
        for (Map.Entry<Location, List<Location>> entry : placedSigns.entrySet()) {
            Location centerLocation = entry.getKey();
            List<Location> signLocations = entry.getValue();
            String groupName = signGroups.get(centerLocation);
            
            // Always refresh signs every 5 minutes regardless of last update time
            refreshSignsForGroup(centerLocation, signLocations, groupName);
            lastUpdateTime.put(centerLocation, currentTime);
        }
        
    }
    
    /**
     * Refreshes signs for a specific group
     * @param centerLocation The center location of the sign grid
     * @param signLocations The list of sign locations
     * @param groupName The group name
     */
    private void refreshSignsForGroup(Location centerLocation, List<Location> signLocations, String groupName) {
        // Get current available cells for this group
        List<Cell> availableCells = cellManager.getAvailableCellsByGroup(groupName);
        
        
        if (availableCells.isEmpty()) {
            // Clear all signs if no available cells
            clearAllSigns(signLocations, groupName);
            return;
        }
        
        
        // Update each sign with the appropriate cell name
        int availableCellIndex = 0;
        int updatedSigns = 0;
        
        for (Location signLocation : signLocations) {
            Block signBlock = signLocation.getBlock();
            
            
            if (signBlock.getType() == Material.WALL_SIGN || signBlock.getType() == Material.SIGN_POST) {
                if (signBlock.getState() instanceof Sign) {
                    Sign sign = (Sign) signBlock.getState();
                    
                    // Clear all lines first
                    for (int i = 0; i < 4; i++) {
                        sign.setLine(i, "");
                    }
                    
                    // Set cell name if we have available cells
                    if (availableCellIndex < availableCells.size()) {
                        Cell cell = availableCells.get(availableCellIndex);
                        sign.setLine(0, "§e" + cell.getName());
                        availableCellIndex++;
                        updatedSigns++;
                    }
                    
                    sign.update();
                } else {
                }
            } else {
            }
        }
        
    }
    
    /**
     * Clears all signs in a group (sets them to empty)
     */
    private void clearAllSigns(List<Location> signLocations, String groupName) {
        
        for (Location signLocation : signLocations) {
            Block signBlock = signLocation.getBlock();
            
            if (signBlock.getType() == Material.WALL_SIGN || signBlock.getType() == Material.SIGN_POST) {
                if (signBlock.getState() instanceof Sign) {
                    Sign sign = (Sign) signBlock.getState();
                    
                    // Clear all lines
                    for (int i = 0; i < 4; i++) {
                        sign.setLine(i, "");
                    }
                    
                    sign.update();
                }
            }
        }
        
    }
    
    /**
     * Gets a comma-separated list of cell names
     */
    private String getCellNames(List<Cell> cells) {
        StringBuilder names = new StringBuilder();
        for (int i = 0; i < cells.size(); i++) {
            if (i > 0) names.append(", ");
            names.append(cells.get(i).getName());
        }
        return names.toString();
    }
}