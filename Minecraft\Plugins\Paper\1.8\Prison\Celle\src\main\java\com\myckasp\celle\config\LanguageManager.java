package com.myckasp.celle.config;

import com.myckasp.celle.Celle;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class LanguageManager {
    
    private final Celle plugin;
    private FileConfiguration langConfig;
    private File langFile;
    private String currentLanguage;
    
    // Danish language messages
    private final Map<String, String> danishMessages = new HashMap<>();
    
    public LanguageManager(Celle plugin) {
        this.plugin = plugin;
        setupLanguage();
        setupDefaultMessages();
    }
    
    private void setupLanguage() {
        langFile = new File(plugin.getDataFolder(), "language.yml");
        
        if (!langFile.exists()) {
            // plugin.getLogger().info("Opretter sprogfil..."); // Removed to reduce console spam
            createDefaultLanguageFile();
        }
        
        langConfig = YamlConfiguration.loadConfiguration(langFile);
        currentLanguage = langConfig.getString("language", "danish");
    }
    
    private void createDefaultLanguageFile() {
        langConfig = YamlConfiguration.loadConfiguration(langFile);
        langConfig.set("language", "danish");
        
        // Command messages
        langConfig.set("messages.command.no-permission", "&cDu har ikke tilladelse til at bruge denne kommando!");
        langConfig.set("messages.command.invalid-syntax", "&cUgyldig syntaks! Brug: /celle <add|remove|list|info>");
        langConfig.set("messages.command.cell-added", "&aCellen '%cellname%' er blevet tilføjet til gruppen '%group%'!");
        langConfig.set("messages.command.cell-not-found", "&cCellen '%cellname%' blev ikke fundet!");
        langConfig.set("messages.command.cell-removed", "&aCellen '%cellname%' er blevet fjernet!");
        langConfig.set("messages.command.list-header", "&eCeller i gruppen '%group%':");
        langConfig.set("messages.command.list-entry", "&7- %cellname% (ejer: %owner%)");
        langConfig.set("messages.command.cell-info", "&eInformation om cellen '%cellname%':");
        langConfig.set("messages.command.cell-info-owner", "&7Ejer: %owner%");
        langConfig.set("messages.command.cell-info-group", "&7Gruppe: %group%");
        langConfig.set("messages.command.cell-info-location", "&7Position: %world%, %x%, %y%, %z%");
        
        // Error messages
        langConfig.set("messages.errors.already-in-cell", "&cDu er allerede i en celle!");
        langConfig.set("messages.errors.no-selection", "&cDu skal markere et område med WorldEdit først!");
        langConfig.set("messages.errors.world-not-enabled", "&cWorldGuard er ikke aktiveret på denne verden!");
        langConfig.set("messages.errors.cell-exists", "&cCellen '%cellname%' eksisterer allerede!");
        langConfig.set("messages.errors.unknown-group", "&cGruppen '%group%' eksisterer ikke!");
        
        // Rental messages
        langConfig.set("messages.rental.cell-not-for-rent", "&cDenne celle kan ikke leases!");
        langConfig.set("messages.rental.cell-rented", "&aDu har lejet cellen '%cellname%' for %days% dage for %price% kr.");
        langConfig.set("messages.rental.cell-already-owned", "&cDu ejer allerede denne celle!");
        langConfig.set("messages.rental.sign-set", "&aSkiltet er blevet opsat for cellen '%cellname%'!");
        langConfig.set("messages.rental.invalid-sign", "&cDu skal kigge på et skilt!");
        langConfig.set("messages.rental.rent-cost", "&eLejepris: %price% kr./dag");
        langConfig.set("messages.rental.max-rent-days", "&eMaks lejeperiode: %days% dage");
        
        // Admin messages
        langConfig.set("messages.admin.no-permission", "&cDu har ikke tilladelse til at bruge admin kommandoen!");
        langConfig.set("messages.admin.group-list-title", "&6Grupper Administration");
        langConfig.set("messages.admin.group-info-title", "&e=== Gruppe Information ===");
        langConfig.set("messages.admin.group-created", "&aGruppen '%group%' er blevet oprettet!");
        langConfig.set("messages.admin.group-updated", "&aGruppen '%group%' er blevet opdateret!");
        langConfig.set("messages.admin.group-deleted", "&aGruppen '%group%' er blevet slettet!");
        langConfig.set("messages.admin.sign-given", "&aDu har modtaget et tilgængelige celler skilt!");
        langConfig.set("messages.admin.sign-placed", "&aTilgængelige celler skilt er blevet oprettet!");
        langConfig.set("messages.admin.sign-updated", "&aSkiltet er blevet opdateret!");
        langConfig.set("messages.admin.invalid-number", "&cUgyldigt tal! Prøv igen.");
        langConfig.set("messages.admin.number-too-small", "&cTallet skal være mindst %min%!");
        langConfig.set("messages.admin.number-too-large", "&cTallet kan ikke være mere end %max%!");
        langConfig.set("messages.admin.cancelled", "&7Annulleret.");
        langConfig.set("messages.admin.cancelled-message", "&7Du kan skrive 'cancel' for at annullere.");
        langConfig.set("messages.admin.confirm-required", "&cBekræft sletning af gruppen '%group%'");
        langConfig.set("messages.admin.confirm-instruction", "&cSkriv 'confirm' for at slette eller 'cancel' for at annullere");
        langConfig.set("messages.admin.default-group-protected", "&cStandard gruppen kan ikke slettes!");
        langConfig.set("messages.admin.group-not-found", "&cGruppen '%group%' blev ikke fundet!");
        langConfig.set("messages.admin.sign-info", "&eDette skilt viser tilgængelige celler for gruppen: %group%");
        
        // Update messages
        langConfig.set("messages.update.available", "&cEn ny version af CelleSystem er tilgængelig! &7(Du bruger %current%, nyeste er %latest%)");
        langConfig.set("messages.update.not-available", "&aCelleSystem er allerede opdateret til den nyeste version!");
        langConfig.set("messages.update.no-new-update", "&aDer er ingen nye opdateringer til CelleSystem.");
        langConfig.set("messages.update.download", "&eDownload den nye version: &ahttps://github.com/MyckasP/CelleSystem-Latest");
        
        // Console messages
        langConfig.set("console.update-checking", "[Celle] Tjekker for opdateringer...");
        langConfig.set("console.update-available", "[Celle] En ny version af CelleSystem er tilgængelig! Version: %version%");
        langConfig.set("console.update-download", "[Celle] Download på: https://github.com/MyckasP/CelleSystem-Latest");
        langConfig.set("console.update-up-to-date", "[Celle] CelleSystem er opdateret til den nyeste version!");
        langConfig.set("console.update-check-failed", "[Celle] Kunne ikke tjekke for opdateringer. Antager ingen opdateringer er tilgængelige.");
        
        try {
            langConfig.save(langFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Kunne ikke gemme sprogfil: " + e.getMessage());
        }
    }
    
    private void setupDefaultMessages() {
        danishMessages.put("command.no-permission", "&cDu har ikke tilladelse til at bruge denne kommando!");
        danishMessages.put("command.invalid-syntax", "&cUgyldig syntaks! Brug: /celle <add|remove|list|info>");
        danishMessages.put("command.cell-added", "&aCellen '%cellname%' er blevet tilføjet til gruppen '%group%'!");
        danishMessages.put("command.cell-not-found", "&cCellen '%cellname%' blev ikke fundet!");
        danishMessages.put("command.cell-removed", "&aCellen '%cellname%' er blevet fjernet!");
        danishMessages.put("command.list-header", "&eCeller i gruppen '%group%':");
        danishMessages.put("command.list-entry", "&7- %cellname% (ejer: %owner%)");
        danishMessages.put("command.cell-info", "&eInformation om cellen '%cellname%':");
        danishMessages.put("command.cell-info-owner", "&7Ejer: %owner%");
        danishMessages.put("command.cell-info-group", "&7Gruppe: %group%");
        danishMessages.put("command.cell-info-location", "&7Position: %world%, %x%, %y%, %z%");
        danishMessages.put("errors.already-in-cell", "&cDu er allerede i en celle!");
        danishMessages.put("errors.no-selection", "&cDu skal markere et område med WorldEdit først!");
        danishMessages.put("errors.world-not-enabled", "&cWorldGuard er ikke aktiveret på denne verden!");
        danishMessages.put("errors.cell-exists", "&cCellen '%cellname%' eksisterer allerede!");
        danishMessages.put("errors.unknown-group", "&cGruppen '%group%' eksisterer ikke!");
        
        // Rental messages
        danishMessages.put("rental.cell-not-for-rent", "&cDenne celle kan ikke leases!");
        danishMessages.put("rental.cell-rented", "&aDu har lejet cellen '%cellname%' for %days% dage for %price% kr.");
        danishMessages.put("rental.cell-already-owned", "&cDu ejer allerede denne celle!");
        danishMessages.put("rental.sign-set", "&aSkiltet er blevet opsat for cellen '%cellname%'!");
        danishMessages.put("rental.invalid-sign", "&cDu skal kigge på et skilt!");
        danishMessages.put("rental.rent-cost", "&eLejepris: %price% kr./dag");
        danishMessages.put("rental.max-rent-days", "&eMaks lejeperiode: %days% dage");
        
        // Admin messages
        danishMessages.put("admin.no-permission", "&cDu har ikke tilladelse til at bruge admin kommandoen!");
        danishMessages.put("admin.group-list-title", "&6Grupper Administration");
        danishMessages.put("admin.group-info-title", "&e=== Gruppe Information ===");
        danishMessages.put("admin.group-created", "&aGruppen '%group%' er blevet oprettet!");
        danishMessages.put("admin.group-updated", "&aGruppen '%group%' er blevet opdateret!");
        danishMessages.put("admin.group-deleted", "&aGruppen '%group%' er blevet slettet!");
        danishMessages.put("admin.sign-given", "&aDu har modtaget et tilgængelige celler skilt!");
        danishMessages.put("admin.sign-placed", "&aTilgængelige celler skilt er blevet oprettet!");
        danishMessages.put("admin.sign-updated", "&aSkiltet er blevet opdateret!");
        danishMessages.put("admin.invalid-number", "&cUgyldigt tal! Prøv igen.");
        danishMessages.put("admin.number-too-small", "&cTallet skal være mindst %min%!");
        danishMessages.put("admin.number-too-large", "&cTallet kan ikke være mere end %max%!");
        danishMessages.put("admin.cancelled", "&7Annulleret.");
        danishMessages.put("admin.cancelled-message", "&7Du kan skrive 'cancel' for at annullere.");
        danishMessages.put("admin.confirm-required", "&cBekræft sletning af gruppen '%group%'");
        danishMessages.put("admin.confirm-instruction", "&cSkriv 'confirm' for at slette eller 'cancel' for at annullere");
        danishMessages.put("admin.default-group-protected", "&cStandard gruppen kan ikke slettes!");
        danishMessages.put("admin.group-not-found", "&cGruppen '%group%' blev ikke fundet!");
        danishMessages.put("admin.sign-info", "&eDette skilt viser tilgængelige celler for gruppen: %group%");
        
        // Update messages
        danishMessages.put("update.available", "&cEn ny version af CelleSystem er tilgængelig! &7(Du bruger %current%, nyeste er %latest%)");
        danishMessages.put("update.not-available", "&aCelleSystem er allerede opdateret til den nyeste version!");
        danishMessages.put("update.no-new-update", "&aDer er ingen nye opdateringer til CelleSystem.");
        danishMessages.put("update.download", "&eDownload den nye version: &ahttps://github.com/MyckasP/CelleSystem-Latest");
        
        // Console messages
        danishMessages.put("console.update-checking", "[Celle] Tjekker for opdateringer...");
        danishMessages.put("console.update-available", "[Celle] En ny version af CelleSystem er tilgængelig! Version: %version%");
        danishMessages.put("console.update-download", "[Celle] Download på: https://github.com/MyckasP/CelleSystem-Latest");
        danishMessages.put("console.update-up-to-date", "[Celle] CelleSystem er opdateret til den nyeste version!");
        danishMessages.put("console.update-check-failed", "[Celle] Kunne ikke tjekke for opdateringer. Antager ingen opdateringer er tilgængelige.");
    }
    
    public String getMessage(String key) {
        // Handle console messages which are not under messages.*
        if (key.startsWith("console.")) {
            return colorize(langConfig.getString(key, danishMessages.getOrDefault(key, "&cMelding ikke fundet: " + key)));
        }
        
        return colorize(langConfig.getString("messages." + key, danishMessages.getOrDefault(key, "&cMelding ikke fundet: " + key)));
    }
    
    public String getMessage(String key, String... replacements) {
        String message = getMessage(key);
        
        // Handle named placeholders first (e.g., %cellname%, %group%, etc.)
        message = message.replace("%cellname%", replacements.length > 0 ? replacements[0] : "");
        message = message.replace("%group%", replacements.length > 1 ? replacements[1] : "");
        message = message.replace("%owner%", replacements.length > 2 ? replacements[2] : "");
        message = message.replace("%world%", replacements.length > 3 ? replacements[3] : "");
        message = message.replace("%x%", replacements.length > 4 ? replacements[4] : "");
        message = message.replace("%y%", replacements.length > 5 ? replacements[5] : "");
        message = message.replace("%z%", replacements.length > 6 ? replacements[6] : "");
        message = message.replace("%days%", replacements.length > 7 ? replacements[7] : "");
        message = message.replace("%price%", replacements.length > 8 ? replacements[8] : "");
        message = message.replace("%version%", replacements.length > 9 ? replacements[9] : "");
        
        // Then handle positional placeholders for backward compatibility (e.g., %1%, %2%, etc.)
        for (int i = 0; i < replacements.length; i++) {
            message = message.replace("%" + (i + 1) + "%", replacements[i]);
        }
        
        return message;
    }
    
    public String getMessage(String key, Map<String, String> replacements) {
        String message = getMessage(key);
        
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            message = message.replace(entry.getKey(), entry.getValue());
        }
        
        return message;
    }
    
    public void reloadLanguage() {
        try {
            langConfig.load(langFile);
            currentLanguage = langConfig.getString("language", "danish");
        } catch (IOException e) {
            plugin.getLogger().severe("Kunne ikke indlæse sprogfil: " + e.getMessage());
        } catch (Exception e) {
            plugin.getLogger().severe("Fejl ved indlæsning af sprogfil: " + e.getMessage());
        }
    }
    
    public String getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * Converts color codes (&a, &c, etc.) to ChatColor equivalents
     * @param text The text with color codes
     * @return The text with converted color codes
     */
    private String colorize(String text) {
        if (text == null) {
            return "";
        }
        return ChatColor.translateAlternateColorCodes('&', text);
    }
}