package com.myckasp.celle.utils;

import com.myckasp.celle.Celle;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;

/**
 * Utility class for validating licenses with the MyckasPlugins API
 */
public class LicenseValidator {
    
    private static final String API_URL = "https://myckasplugins.flyfiles.dk/api/licenses/validate";
    private static final String PLUGIN_ID = "cells";
    private static final String USER_AGENT = "CelleSystem/1.0.0";
    
    private final Celle plugin;
    private final HttpClient httpClient;
    
    public LicenseValidator(Celle plugin) {
        this.plugin = plugin;
        
        // Configure HTTP client with timeout
        int timeout = plugin.getConfigManager().getInt("license.validation.timeout", 10) * 1000;
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setSocketTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .build();
        
        this.httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(config)
                .setUserAgent(USER_AGENT)
                .build();
    }
    
    /**
     * Validates a license key synchronously
     * @param licenseKey The license key to validate
     * @return LicenseValidationResult containing validation results
     */
    public LicenseValidationResult validateLicense(String licenseKey) {
        if (licenseKey == null || licenseKey.trim().isEmpty() || "YOUR_LICENSE_KEY_HERE".equals(licenseKey)) {
            return new LicenseValidationResult(false, "No license key provided", false, null, null, null);
        }
        
        try {
            HttpPost post = new HttpPost(API_URL);
            post.setHeader("Content-Type", "application/json");
            post.setHeader("User-Agent", USER_AGENT);
            
            // Create JSON request body
            String jsonBody = String.format(
                "{\"licenseKey\":\"%s\",\"pluginId\":\"%s\"}", 
                licenseKey.replace("\"", "\\\""), 
                PLUGIN_ID
            );
            
            post.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));
            
            HttpResponse response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            
            if (entity != null) {
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                return parseResponse(responseBody, response.getStatusLine().getStatusCode());
            } else {
                return new LicenseValidationResult(false, "Empty response from license server", false, null, null, null);
            }
            
        } catch (IOException e) {
            plugin.getLogger().log(Level.WARNING, "Failed to validate license: " + e.getMessage());
            return new LicenseValidationResult(false, "Connection error: " + e.getMessage(), false, null, null, null);
        }
    }
    
    /**
     * Validates a license key asynchronously
     * @param licenseKey The license key to validate
     * @return CompletableFuture containing the validation result
     */
    public CompletableFuture<LicenseValidationResult> validateLicenseAsync(String licenseKey) {
        CompletableFuture<LicenseValidationResult> future = new CompletableFuture<>();
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    LicenseValidationResult result = validateLicense(licenseKey);
                    future.complete(result);
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            }
        }.runTaskAsynchronously(plugin);
        
        return future;
    }
    
    /**
     * Parses the JSON response from the license API
     * @param responseBody The JSON response body
     * @param statusCode The HTTP status code
     * @return LicenseValidationResult containing parsed results
     */
    private LicenseValidationResult parseResponse(String responseBody, int statusCode) {
        try {
            // Simple JSON parsing without external libraries
            boolean valid = responseBody.contains("\"valid\":true");
            boolean expired = responseBody.contains("\"expired\":true");
            
            String message = extractJsonValue(responseBody, "message");
            String pluginName = extractJsonValue(responseBody, "pluginName");
            String expiresAt = extractJsonValue(responseBody, "expiresAt");
            
            if (statusCode != 200) {
                return new LicenseValidationResult(false, message != null ? message : "Server error", expired, null, pluginName, expiresAt);
            }
            
            return new LicenseValidationResult(valid, message, expired, PLUGIN_ID, pluginName, expiresAt);
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to parse license response: " + e.getMessage());
            return new LicenseValidationResult(false, "Failed to parse server response", false, null, null, null);
        }
    }
    
    /**
     * Simple JSON value extraction
     * @param json The JSON string
     * @param key The key to extract
     * @return The extracted value or null if not found
     */
    private String extractJsonValue(String json, String key) {
        String searchPattern = "\"" + key + "\":\"";
        int startIndex = json.indexOf(searchPattern);
        if (startIndex == -1) {
            return null;
        }
        
        startIndex += searchPattern.length();
        int endIndex = json.indexOf("\"", startIndex);
        if (endIndex == -1) {
            return null;
        }
        
        return json.substring(startIndex, endIndex);
    }
    
    /**
     * Result class for license validation
     */
    public static class LicenseValidationResult {
        private final boolean valid;
        private final String message;
        private final boolean expired;
        private final String pluginId;
        private final String pluginName;
        private final String expiresAt;
        
        public LicenseValidationResult(boolean valid, String message, boolean expired, String pluginId, String pluginName, String expiresAt) {
            this.valid = valid;
            this.message = message;
            this.expired = expired;
            this.pluginId = pluginId;
            this.pluginName = pluginName;
            this.expiresAt = expiresAt;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
        
        public boolean isExpired() {
            return expired;
        }
        
        public String getPluginId() {
            return pluginId;
        }
        
        public String getPluginName() {
            return pluginName;
        }
        
        public String getExpiresAt() {
            return expiresAt;
        }
        
        @Override
        public String toString() {
            return String.format("LicenseValidationResult{valid=%s, message='%s', expired=%s, pluginId='%s', pluginName='%s', expiresAt='%s'}", 
                    valid, message, expired, pluginId, pluginName, expiresAt);
        }
    }
}
