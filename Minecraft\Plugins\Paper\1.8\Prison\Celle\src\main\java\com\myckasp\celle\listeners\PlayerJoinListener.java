package com.myckasp.celle.listeners;

import com.myckasp.celle.Celle;
import com.myckasp.celle.managers.UpdateManager;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.entity.Player;

public class PlayerJoinListener implements Listener {
    
    private final Celle plugin;
    
    public PlayerJoinListener(Celle plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Only notify players with op or admin permissions
        if (player.isOp() || player.hasPermission("celle.admin") || player.hasPermission("celle.*")) {
            // Schedule the notification to happen 2 seconds after player joins
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    UpdateManager updateManager = plugin.getUpdateManager();

                    // Check if updates are available and notify the player
                    // Use the checkForUpdatesAndNotify method which ensures async completion
                    updateManager.checkForUpdatesAndNotify(player);
                }
            }.runTaskLater(plugin, 60); // 60 ticks = 3 seconds (20 ticks = 1 second)
        }
    }
}