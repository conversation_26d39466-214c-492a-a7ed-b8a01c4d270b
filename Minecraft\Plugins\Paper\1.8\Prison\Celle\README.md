# Celle - Minecraft Prison Cells Plugin

Et plugin til at administrere fængselsceller i Minecraft 1.8, udviklet af MyckasP.

## Oversigt

Dette plugin er designet til at give servere mulighed for at administrere fængselsceller med WorldGuard/WorldEdit integration. Pluginet er skrevet i Java og bruger Spigot API.

## Krav

- Minecraft 1.8 Server
- Spigot/Paper
- WorldGuard
- WorldEdit

## Installation

1. Download plugin filen (Celle.jar)
2. Placer filen i din servers plugins folder
3. Genstart serveren
4. Konfigurer pluginet via config.yml og language.yml filerne

## Kommandoer

### /celle add <cellname> [group]
Opretter en ny celle baseret på din WorldEdit markering.

**Parametre:**
- `cellname`: Navnet på cellen
- `group`: Valgfri gruppe (standard: "default")

**Eksempel:**
```
/celle add celle1
/celle add celle2 premium
```

### /celle remove <cellname>
Fjerner en celle.

**Parametre:**
- `cellname`: Navnet på cellen der skal fjernes

**Eksempel:**
```
/celle remove celle1
```

### /celle list [group]
Lister alle celler eller celler i en specifik gruppe.

**Parametre:**
- `group`: Valgfri gruppe (standard: alle celler)

**Eksempel:**
```
/celle list
/celle list default
/celle list premium
```

### /celle info <cellname>
Viser information om en specifik celle.

**Parametre:**
- `cellname`: Navnet på cellen

**Eksempel:**
```
/celle info celle1
```

## Projekt Struktur

```
Celle/
├── src/
│   └── main/
│       └── java/
│           └── com/
│               └── myckasp/
│                   └── celle/
│                       ├── Celle.java                 # Hoved plugin klasse
│                       ├── commands/
│                       │   └── CelleCommand.java      # Kommando håndtering
│                       ├── config/
│                       │   ├── ConfigManager.java     # Konfigurations håndtering
│                       │   └── LanguageManager.java   # Sprog håndtering
│                       ├── managers/
│                       │   └── CellManager.java       # Celle data håndtering
│                       ├── models/
│                       │   ├── Cell.java              # Celle model
│                       │   └── CellGroup.java         # Celle gruppe model
│                       └── utils/
│                           └── WorldGuardUtils.java   # WorldGuard integration
├── plugin.yml                  # Plugin metadata
├── pom.xml                     # Maven build konfiguration
└── README.md                   # Dokumentation
```

## Maven Build

For at bygge projektet skal du tilføje følgende afhængigheder til dit Maven projekt:

```xml
<!-- Spigot API -->
<dependency>
    <groupId>org.spigotmc</groupId>
    <artifactId>spigot-api</artifactId>
    <version>1.8.8-R0.1-SNAPSHOT</version>
    <scope>provided</scope>
</dependency>

<!-- WorldGuard -->
<dependency>
    <groupId>com.sk89q.worldguard</groupId>
    <artifactId>worldguard-bukkit</artifactId>
    <version>7.0.0</version>
    <scope>provided</scope>
</dependency>

<!-- WorldEdit -->
<dependency>
    <groupId>com.sk89q.worldedit</groupId>
    <artifactId>worldedit-bukkit</artifactId>
    <version>7.1.0</version>
    <scope>provided</scope>
</dependency>
```

## Grupper

Celler kan organiseres i grupper for nem administration. Standard gruppen "default" oprettes automatisk.

### Grupper i fremtidige versioner

Grupper-systemet er designet til fremtidig udvidelse og kan bruges til:
- Forskellige cell størrelser
- Forskellige priser
- Forskellige tilladelser
- Forskellige funktioner

## Konfiguration

### config.yml
Standard konfigurationsfil for plugin indstillinger.

### language.yml
Sprogfiler med danske beskeder. Kan tilpasses efter behov.

## Udvikling

### Kompilering

For at kompilere projektet skal du bruge:
- Java Development Kit (JDK) 8 eller nyere
- Maven

```bash
mvn clean package
```

### Tilføjelse af nye funktioner

1. Opret nye klasser i passende pakker
2. Opdater managers hvis nødvendigt
3. Tilføj nye kommandoer i CelleCommand
4. Opdater sprogfiler i LanguageManager
5. Dokumenter ændringerne

## Licens

Dette plugin er udviklet af MyckasP og kan frit bruges og modificeres.

## Bidrag

Hvis du vil bidrage til udviklingen af pluginet, er du velkommen til at:
1. Oprette et issue med forslag eller fejlrapporter
2. Send pull requests med forbedringer
3. Hjælpe med at oversætte til andre sprog

## Kontakt

Udvikler: MyckasP