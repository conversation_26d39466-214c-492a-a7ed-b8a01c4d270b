package com.myckasp.celle.commands;

import com.myckasp.celle.Celle;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.models.Cell;
import com.myckasp.celle.models.CellGroup;
import com.myckasp.celle.utils.WorldGuardUtils;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.UUID;

public class CelleCommand implements CommandExecutor {
    
    private final Celle plugin;
    
    public CelleCommand(Celle plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Denne kommando kan kun bruges af spillere!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            sendHelp(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        // Allow license command even when plugin is not licensed
        if (subCommand.equals("license")) {
            if (!player.hasPermission("celle.admin") && !player.hasPermission("celle.*")) {
                player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                return true;
            }
            handleLicenseCommand(player, args);
            return true;
        }

        // Check if plugin is licensed before allowing other commands
        if (!plugin.isLicensed()) {
            player.sendMessage("§c[CelleSystem] Plugin er ikke licenseret! Kontakt en administrator.");
            player.sendMessage("§c[CelleSystem] Brug /celle license for at tjekke licens status.");
            return true;
        }
        
        switch (subCommand) {
            case "admin":
                if (!player.hasPermission("celle.admin") && !player.hasPermission("celle.*")) {
                    player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                    return true;
                }
                // Forward to AdminCommand
                plugin.getAdminCommand().onCommand(sender, command, label, new String[0]);
                break;
            case "add":
                if (!player.hasPermission("celle.add") && !player.hasPermission("celle.*")) {
                    player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                    return true;
                }
                handleAddCommand(player, args);
                break;
            case "remove":
                if (!player.hasPermission("celle.remove") && !player.hasPermission("celle.*")) {
                    player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                    return true;
                }
                handleRemoveCommand(player, args);
                break;
            case "list":
                if (!player.hasPermission("celle.list") && !player.hasPermission("celle.*")) {
                    player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                    return true;
                }
                handleListCommand(player, args);
                break;
            case "info":
                if (!player.hasPermission("celle.info") && !player.hasPermission("celle.*")) {
                    player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                    return true;
                }
                handleInfoCommand(player, args);
                break;
            case "reload":
                if (!player.hasPermission("celle.reload") && !player.hasPermission("celle.*")) {
                    player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
                    return true;
                }
                handleReloadCommand(player);
                break;
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void sendHelp(Player player) {
        player.sendMessage("§e=== CelleSystem Kommandoer ===");
        
        // Show admin command if player has permission
        if (player.hasPermission("celle.admin") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle admin §7- Åbn admin menu");
        }
        
        // Show add command if player has permission
        if (player.hasPermission("celle.add") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle add <cellname> [group] §7- Tilføj en ny celle (kig på et skift for at sætte lejeskilte)");
        }
        
        // Show remove command if player has permission
        if (player.hasPermission("celle.remove") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle remove <cellname> §7- Fjern en celle");
        }
        
        // Show list command if player has permission
        if (player.hasPermission("celle.list") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle list [group] §7- List alle celler");
        }
        
        // Show info command if player has permission
        if (player.hasPermission("celle.info") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle info <cellname> §7- Se information om en celle");
        }
        
        // Show reload command if player has permission
        if (player.hasPermission("celle.reload") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle reload §7- Genindlæs konfigurationsfiler og opdater skilte");
        }

        // Show license command if player has permission
        if (player.hasPermission("celle.admin") || player.hasPermission("celle.*")) {
            player.sendMessage("§6/celle license §7- Tjek licens status og genindlæs licens");
        }

        // If player has no permissions for any command
        if (!player.hasPermission("celle.admin") && !player.hasPermission("celle.add") && !player.hasPermission("celle.remove") &&
            !player.hasPermission("celle.list") && !player.hasPermission("celle.info") &&
            !player.hasPermission("celle.reload") && !player.hasPermission("celle.*")) {
            player.sendMessage("§cDu har ikke tilladelse til at bruge nogen af cellekommandoerne.");
        }
    }
    
    private void handleAddCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle add <cellname> [group]");
            return;
        }
        
        String cellName = args[1];
        String groupName = args.length > 2 ? args[2] : "default";
        
        CellManager cellManager = plugin.getCellManager();
        WorldGuardUtils worldGuardUtils = plugin.getWorldGuardUtils();

        // Check if cell already exists (case-insensitive)
        if (cellManager.cellExistsIgnoreCase(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.cell-exists", cellName));
            return;
        }

        // Check if group exists
        if (!cellManager.groupExists(groupName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.unknown-group", groupName));
            return;
        }

        // Check if player has WorldEdit selection
        if (!worldGuardUtils.hasSelection(player)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.no-selection"));
            return;
        }

        // Get selection
        Location[] selection = worldGuardUtils.getSelection(player);
        if (selection == null || selection.length != 2) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.no-selection"));
            return;
        }

        Location min = selection[0];
        Location max = selection[1];

        // Create WorldGuard region
        if (!worldGuardUtils.createProtectedRegion(player, cellName, min, max)) {
            player.sendMessage("§cKunne ikke oprette WorldGuard region!");
            return;
        }
        
        // Create cell
        UUID ownerUUID = player.getUniqueId();
        cellManager.createCell(cellName, min, max, ownerUUID, groupName);
        
        // Check if player is looking at a sign
        org.bukkit.block.Block block = player.getTargetBlock((java.util.Set<org.bukkit.Material>) null, 5);
        
        if (block.getState() instanceof org.bukkit.block.Sign) {
            // Player is looking at a sign, set it as rental sign
            org.bukkit.block.Sign sign = (org.bukkit.block.Sign) block.getState();
            
            // Set the sign text
            // First line: "TIL LEJE" with dark green and bold
            sign.setLine(0, "§2§lTIL LEJE");
            
            // Second line: Cell name with green color
            sign.setLine(1, "§a" + cellName);
            
            // Third line: Price per day with green color (in $ format)
            CellGroup group = cellManager.getGroup(groupName);
            double rentPrice = group != null ? group.getDefaultRentPrice() : 350.0;
            sign.setLine(2, "§a$" + (int)rentPrice);
            
            // Fourth line: Default rental days with green color
            sign.setLine(3, "§a1 dag");
            
            // Update the sign
            sign.update();
            
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-added", cellName, groupName));
            player.sendMessage(plugin.getLanguageManager().getMessage("rental.sign-set", cellName));
        } else {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-added", cellName, groupName));
            player.sendMessage("§7Tip: Kig på et skilt og genkør kommandoen for at sætte et lejeskilte!");
        }
    }
    
    private void handleRemoveCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle remove <cellname>");
            return;
        }
        
        String cellName = args[1];
        CellManager cellManager = plugin.getCellManager();
        WorldGuardUtils worldGuardUtils = plugin.getWorldGuardUtils();

        // Check if cell exists (case-insensitive)
        if (!cellManager.cellExistsIgnoreCase(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-not-found", cellName));
            return;
        }

        Cell cell = cellManager.getCellIgnoreCase(cellName);

        // Check if player is owner or admin
        if (!cell.isOwner(player) && !player.hasPermission("celle.admin")) {
            player.sendMessage("§cDu ejer ikke denne celle!");
            return;
        }

        // Remove WorldGuard region
        worldGuardUtils.deleteProtectedRegion(player, cellName);
        
        // Update all rental signs for this cell to show it's been removed
        updateAllRentalSignsForCell(cellName);
        
        // Remove cell
        cellManager.removeCell(cellName);
        
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-removed", cellName));
    }
    
    private void handleListCommand(Player player, String[] args) {
        String groupName = args.length > 1 ? args[1] : null;
        CellManager cellManager = plugin.getCellManager();
        
        if (groupName == null) {
            // List all cells
            player.sendMessage("§e=== Alle Celler (CelleSystem) ===");
            for (Cell cell : cellManager.getAllCells()) {
                String ownerName = plugin.getServer().getOfflinePlayer(cell.getOwner()).getName();
                player.sendMessage("§7- " + cell.getName() + " (ejer: " + ownerName + ", gruppe: " + cell.getGroupName() + ")");
            }
        } else {
            // List cells in specific group
            if (!cellManager.groupExists(groupName)) {
                player.sendMessage(plugin.getLanguageManager().getMessage("errors.unknown-group", groupName));
                return;
            }
            
            player.sendMessage(plugin.getLanguageManager().getMessage("command.list-header", groupName));
            for (Cell cell : cellManager.getCellsByGroup(groupName)) {
                String ownerName = plugin.getServer().getOfflinePlayer(cell.getOwner()).getName();
                player.sendMessage(plugin.getLanguageManager().getMessage("command.list-entry", cell.getName(), ownerName));
            }
        }
    }
    
    private void handleInfoCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle info <cellname>");
            return;
        }
        
        String cellName = args[1];
        CellManager cellManager = plugin.getCellManager();
        
        // Check if cell exists (case-insensitive)
        if (!cellManager.cellExistsIgnoreCase(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-not-found", cellName));
            return;
        }
        
        Cell cell = cellManager.getCellIgnoreCase(cellName);
        String ownerName = plugin.getServer().getOfflinePlayer(cell.getOwner()).getName();
        
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info", cellName));
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info-owner", ownerName));
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info-group", cell.getGroupName()));
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info-location",
            cell.getWorld().getName(),
            String.valueOf((int) cell.getCenterX()),
            String.valueOf((int) cell.getCenterY()),
            String.valueOf((int) cell.getCenterZ())));
    }
    
    private void handleReloadCommand(Player player) {
        // Reload config files
        plugin.getConfigManager().reloadConfig();
        plugin.getLanguageManager().reloadLanguage();
        plugin.getCellManager().reloadCells();
        
        // Update all rental signs
        updateAllRentalSigns();
        
        player.sendMessage("§aKonfigurationsfiler er genindlæst!");
        player.sendMessage("§aAlle skilte er blevet opdateret!");
    }
    
    private void updateAllRentalSigns() {
        // Search for all rental signs in loaded chunks
        for (org.bukkit.World world : plugin.getServer().getWorlds()) {
            // Get all loaded chunks in the world
            for (org.bukkit.Chunk chunk : world.getLoadedChunks()) {
                // Check all block entities in the chunk for signs
                for (org.bukkit.block.BlockState blockState : chunk.getTileEntities()) {
                    if (blockState instanceof org.bukkit.block.Sign) {
                        org.bukkit.block.Sign sign = (org.bukkit.block.Sign) blockState;
                        
                        // Check if this is a rental sign for a cell
                        if (sign.getLine(0).equals("§2§lTIL LEJE")) {
                            String cellName = sign.getLine(1).replace("§a", "");
                            
                            // Find the cell
                            Cell cell = plugin.getCellManager().getCellIgnoreCase(cellName);
                            if (cell != null && cell.isForRent()) {
                                // Get the price from the cell
                                double rentPrice = cell.getRentPrice();
                                
                                // Update the sign with the new price (without .0)
                                sign.setLine(2, "§a$" + (int)rentPrice);
                                sign.update();
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Updates all rental signs for a specific cell to show it's been removed
     * @param cellName The name of the cell that was removed
     */
    private void updateAllRentalSignsForCell(String cellName) {
        // Search for all rental signs in loaded chunks
        for (org.bukkit.World world : plugin.getServer().getWorlds()) {
            // Get all loaded chunks in the world
            for (org.bukkit.Chunk chunk : world.getLoadedChunks()) {
                // Check all block entities in the chunk for signs
                for (org.bukkit.block.BlockState blockState : chunk.getTileEntities()) {
                    if (blockState instanceof org.bukkit.block.Sign) {
                        org.bukkit.block.Sign sign = (org.bukkit.block.Sign) blockState;
                        
                        // Check if this is a rental sign for the specified cell
                        if (sign.getLine(0).equals("§2§lTIL LEJE") &&
                            sign.getLine(1).replace("§a", "").equals(cellName)) {
                            
                            // Set the sign text to indicate cell is removed
                            sign.setLine(0, "§c§lSLETTET");
                            sign.setLine(1, "§cCellen er");
                            sign.setLine(2, "§cfjernet");
                            sign.setLine(3, "§c---");
                            
                            // Update the sign
                            sign.update();
                            
                        }
                    }
                }
            }
        }
    }

    /**
     * Handles the license command
     * @param player The player executing the command
     * @param args Command arguments
     */
    private void handleLicenseCommand(Player player, String[] args) {
        if (args.length == 1) {
            // Show license status
            showLicenseStatus(player);
        } else if (args.length == 2) {
            String subCommand = args[1].toLowerCase();

            switch (subCommand) {
                case "status":
                    showLicenseStatus(player);
                    break;
                case "reload":
                    reloadLicense(player);
                    break;
                case "check":
                    checkLicense(player);
                    break;
                default:
                    player.sendMessage("§cUgyldig syntaks! Brug: /celle license [status|reload|check]");
                    break;
            }
        } else {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle license [status|reload|check]");
        }
    }

    /**
     * Shows the current license status to the player
     * @param player The player to show the status to
     */
    private void showLicenseStatus(Player player) {
        player.sendMessage("§e=== CelleSystem Licens Status ===");

        if (plugin.getLicenseManager() == null) {
            player.sendMessage("§cLicens manager er ikke initialiseret!");
            return;
        }

        boolean isValid = plugin.getLicenseManager().isLicenseValid();
        boolean isChecked = plugin.getLicenseManager().isLicenseChecked();
        String licenseKey = plugin.getLicenseManager().getLicenseKey();
        String lastMessage = plugin.getLicenseManager().getLastValidationMessage();

        // License status
        if (!isChecked) {
            player.sendMessage("§eLicens Status: §6Ikke tjekket endnu");
        } else if (isValid) {
            player.sendMessage("§aLicens Status: §2Gyldig");
        } else {
            player.sendMessage("§cLicens Status: §4Ugyldig");
        }

        // License key (masked)
        player.sendMessage("§eLicens Nøgle: §7" + licenseKey);

        // Last validation message
        if (lastMessage != null && !lastMessage.isEmpty()) {
            player.sendMessage("§eSidste besked: §7" + lastMessage);
        }

        // Plugin functionality status
        if (plugin.isLicensed()) {
            player.sendMessage("§aPlugin Status: §2Fuldt funktionsdygtig");
        } else {
            player.sendMessage("§cPlugin Status: §4Begrænset funktionalitet");
        }

        player.sendMessage("§7Brug §e/celle license reload §7for at genindlæse licensen");
        player.sendMessage("§7Brug §e/celle license check §7for at tjekke licensen nu");
    }

    /**
     * Reloads the license configuration and revalidates
     * @param player The player executing the command
     */
    private void reloadLicense(Player player) {
        player.sendMessage("§eGenindlæser licens konfiguration...");

        if (plugin.getLicenseManager() == null) {
            player.sendMessage("§cLicens manager er ikke initialiseret!");
            return;
        }

        plugin.getLicenseManager().reloadLicense().thenAccept(success -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (success) {
                    player.sendMessage("§aLicens konfiguration genindlæst og valideret!");
                    if (plugin.isLicensed()) {
                        player.sendMessage("§aPlugin er nu fuldt funktionsdygtig!");
                    } else {
                        player.sendMessage("§cPlugin kører stadig i begrænset tilstand!");
                    }
                } else {
                    player.sendMessage("§cFejl ved genindlæsning af licens!");
                    String lastMessage = plugin.getLicenseManager().getLastValidationMessage();
                    if (lastMessage != null && !lastMessage.isEmpty()) {
                        player.sendMessage("§cFejl: " + lastMessage);
                    }
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                player.sendMessage("§cFejl ved genindlæsning af licens: " + throwable.getMessage());
            });
            return null;
        });
    }

    /**
     * Manually checks the license
     * @param player The player executing the command
     */
    private void checkLicense(Player player) {
        player.sendMessage("§eTjekker licens...");

        if (plugin.getLicenseManager() == null) {
            player.sendMessage("§cLicens manager er ikke initialiseret!");
            return;
        }

        plugin.getLicenseManager().validateLicenseAsync().thenAccept(result -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (result.isValid()) {
                    player.sendMessage("§aLicens er gyldig!");
                    player.sendMessage("§aPlugin: " + (result.getPluginName() != null ? result.getPluginName() : "CelleSystem"));
                    if (result.getExpiresAt() != null) {
                        player.sendMessage("§eUdløber: " + result.getExpiresAt());
                    }
                } else {
                    player.sendMessage("§cLicens validering fejlede!");
                    player.sendMessage("§cFejl: " + result.getMessage());
                    if (result.isExpired()) {
                        player.sendMessage("§cDin licens er udløbet!");
                    }
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                player.sendMessage("§cFejl ved licens tjek: " + throwable.getMessage());
            });
            return null;
        });
    }

}