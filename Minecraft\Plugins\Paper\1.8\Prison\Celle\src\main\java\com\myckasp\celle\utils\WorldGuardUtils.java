package com.myckasp.celle.utils;

import com.sk89q.worldedit.bukkit.WorldEditPlugin;
import com.sk89q.worldedit.bukkit.selections.Selection;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.ProtectedCuboidRegion;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import com.sk89q.worldguard.domains.DefaultDomain;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;

public class WorldGuardUtils {

    private final com.myckasp.celle.Celle plugin;

    public WorldGuardUtils(com.myckasp.celle.Celle plugin) {
        this.plugin = plugin;
    }

    public boolean isWorldGuardEnabled() {
        return plugin.getServer().getPluginManager().isPluginEnabled("WorldGuard");
    }

    public boolean isWorldEditEnabled() {
        return plugin.getServer().getPluginManager().isPluginEnabled("WorldEdit");
    }

    // WorldEdit integration methods
    public boolean hasSelection(Player player) {
        try {
            if (!isWorldEditEnabled()) {
                plugin.getLogger().warning("WorldEdit is not enabled!");
                return false;
            }

            WorldEditPlugin worldEdit = (WorldEditPlugin) plugin.getServer().getPluginManager().getPlugin("WorldEdit");
            if (worldEdit == null) {
                plugin.getLogger().warning("WorldEdit plugin not found!");
                return false;
            }

            Selection selection = worldEdit.getSelection(player);
            return selection != null;

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking WorldEdit selection: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public Location[] getSelection(Player player) {
        try {
            if (!isWorldEditEnabled()) {
                plugin.getLogger().warning("WorldEdit is not enabled!");
                return null;
            }

            WorldEditPlugin worldEdit = (WorldEditPlugin) plugin.getServer().getPluginManager().getPlugin("WorldEdit");
            if (worldEdit == null) {
                plugin.getLogger().warning("WorldEdit plugin not found!");
                return null;
            }

            Selection selection = worldEdit.getSelection(player);
            if (selection == null) {
                plugin.getLogger().warning("Player has no WorldEdit selection!");
                return null;
            }

            Location min = selection.getMinimumPoint();
            Location max = selection.getMaximumPoint();

            return new Location[]{min, max};

        } catch (Exception e) {
            plugin.getLogger().warning("Error getting WorldEdit selection: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    public boolean createProtectedRegion(Player player, String regionName, Location min, Location max) {
        try {
            if (!isWorldGuardEnabled()) {
                plugin.getLogger().warning("WorldGuard is not enabled!");
                return false;
            }

            WorldGuardPlugin worldGuard = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (worldGuard == null) {
                plugin.getLogger().warning("WorldGuard plugin not found!");
                return false;
            }

            RegionManager regionManager = worldGuard.getRegionManager(min.getWorld());
            if (regionManager == null) {
                plugin.getLogger().warning("Could not get region manager for world: " + min.getWorld().getName());
                return false;
            }

            // Check if region already exists
            if (regionManager.hasRegion(regionName)) {
                plugin.getLogger().warning("Region '" + regionName + "' already exists!");
                return false;
            }

            // Create the region using legacy WorldGuard 6.x API
            // For WorldGuard 6.x, we need to use BlockVector objects
            int minX = (int) Math.floor(min.getX());
            int minY = (int) Math.floor(min.getY());
            int minZ = (int) Math.floor(min.getZ());
            int maxX = (int) Math.floor(max.getX());
            int maxY = (int) Math.floor(max.getY());
            int maxZ = (int) Math.floor(max.getZ());

            // Try to create BlockVector objects using reflection or alternative approach
            // Let's try using the WorldEdit API to create vectors
            try {
                // Create BlockVector objects - try different approaches
                Object minPoint = null;
                Object maxPoint = null;

                // Try to create using reflection
                Class<?> blockVectorClass = Class.forName("com.sk89q.worldedit.BlockVector");
                java.lang.reflect.Constructor<?> constructor = blockVectorClass.getConstructor(double.class, double.class, double.class);
                minPoint = constructor.newInstance((double)minX, (double)minY, (double)minZ);
                maxPoint = constructor.newInstance((double)maxX, (double)maxY, (double)maxZ);

                // Create the region using reflection
                java.lang.reflect.Constructor<?> regionConstructor = ProtectedCuboidRegion.class.getConstructor(String.class, blockVectorClass, blockVectorClass);
                ProtectedCuboidRegion region = (ProtectedCuboidRegion) regionConstructor.newInstance(regionName, minPoint, maxPoint);

                // Set the player as owner
                DefaultDomain owners = new DefaultDomain();
                owners.addPlayer(player.getName());
                region.setOwners(owners);

                // Add the region
                regionManager.addRegion(region);

                // Save the regions
                try {
                    regionManager.save();
                } catch (Exception e) {
                    plugin.getLogger().warning("Could not save WorldGuard regions: " + e.getMessage());
                }

                // Remove success log to reduce console spam
                return true;

            } catch (Exception reflectionException) {
                plugin.getLogger().warning("Could not create region using reflection: " + reflectionException.getMessage());
                return false;
            }



        } catch (Exception e) {
            plugin.getLogger().warning("Error creating WorldGuard region: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public boolean deleteProtectedRegion(Player player, String regionName) {
        try {
            if (!isWorldGuardEnabled()) {
                plugin.getLogger().warning("WorldGuard is not enabled!");
                return false;
            }

            WorldGuardPlugin worldGuard = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (worldGuard == null) {
                plugin.getLogger().warning("WorldGuard plugin not found!");
                return false;
            }

            RegionManager regionManager = worldGuard.getRegionManager(player.getWorld());
            if (regionManager == null) {
                plugin.getLogger().warning("Could not get region manager for world: " + player.getWorld().getName());
                return false;
            }

            // Check if region exists
            if (!regionManager.hasRegion(regionName)) {
                plugin.getLogger().warning("Region '" + regionName + "' does not exist!");
                return false;
            }

            // Remove the region
            regionManager.removeRegion(regionName);

            // Save the regions
            try {
                regionManager.save();
            } catch (Exception e) {
                plugin.getLogger().warning("Could not save WorldGuard regions: " + e.getMessage());
            }

            // Remove success log to reduce console spam
            return true;

        } catch (Exception e) {
            plugin.getLogger().warning("Error deleting WorldGuard region: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public List<String> getRegionsAtLocation(Location location) {
        try {
            if (!isWorldGuardEnabled()) {
                return new ArrayList<>();
            }

            WorldGuardPlugin worldGuard = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (worldGuard == null) {
                return new ArrayList<>();
            }

            RegionManager regionManager = worldGuard.getRegionManager(location.getWorld());
            if (regionManager == null) {
                return new ArrayList<>();
            }

            List<String> regions = new ArrayList<>();

            // For WorldGuard 6.x, we need to check regions differently
            // Let's use a simpler approach - check each region manually
            for (ProtectedRegion region : regionManager.getRegions().values()) {
                // For cuboid regions, we can check bounds manually
                if (region instanceof ProtectedCuboidRegion) {
                    ProtectedCuboidRegion cuboid = (ProtectedCuboidRegion) region;
                    // This is a simplified check - in reality we'd need proper vector handling
                    // For now, let's just add all regions as we can't properly check containment
                    regions.add(region.getId());
                }
            }

            return regions;

        } catch (Exception e) {
            plugin.getLogger().warning("Error getting regions at location: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    public boolean isLocationInRegion(Location location, String regionName) {
        try {
            if (!isWorldGuardEnabled()) {
                return false;
            }

            WorldGuardPlugin worldGuard = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (worldGuard == null) {
                return false;
            }

            RegionManager regionManager = worldGuard.getRegionManager(location.getWorld());
            if (regionManager == null) {
                return false;
            }

            ProtectedRegion region = regionManager.getRegion(regionName);
            if (region == null) {
                return false;
            }

            // For WorldGuard 6.x, we need to check containment differently
            // For now, let's return false as we can't properly check without Vector class
            // TODO: Implement proper containment check for WorldGuard 6.x
            plugin.getLogger().warning("Location containment check not fully implemented for WorldGuard 6.x");
            return false;

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking if location is in region: " + e.getMessage());
            return false;
        }
    }

    public boolean playerCanBuildInRegion(Player player, String regionName) {
        try {
            if (!isWorldGuardEnabled()) {
                return true; // If WorldGuard is not available, allow building
            }

            WorldGuardPlugin worldGuard = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (worldGuard == null) {
                return true;
            }

            RegionManager regionManager = worldGuard.getRegionManager(player.getWorld());
            if (regionManager == null) {
                return true;
            }

            ProtectedRegion region = regionManager.getRegion(regionName);
            if (region == null) {
                return true;
            }

            // Check if player can build in the region (is owner or member)
            return region.isOwner(player.getName()) || region.isMember(player.getName());

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking build permission: " + e.getMessage());
            return true;
        }
    }
}