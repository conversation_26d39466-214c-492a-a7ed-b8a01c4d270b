package com.myckasp.celle.models;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;

import java.util.UUID;

public class Cell {
    
    private String name;
    private Location min;
    private Location max;
    private UUID owner;
    private String groupName;
    private boolean forRent;
    private double rentPrice;
    private int maxRentDays;
    private int defaultRentDays;
    
    public Cell(String name, Location min, Location max, UUID owner, String groupName) {
        this.name = name;
        this.min = min;
        this.max = max;
        this.owner = owner;
        this.groupName = groupName;
        this.forRent = true; // Default to for rent
        this.rentPrice = 350.0; // Default price for default group
        this.maxRentDays = 10;
        this.defaultRentDays = 1;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Location getMin() {
        return min;
    }
    
    public void setMin(Location min) {
        this.min = min;
    }
    
    public Location getMax() {
        return max;
    }
    
    public void setMax(Location max) {
        this.max = max;
    }
    
    public UUID getOwner() {
        return owner;
    }
    
    public void setOwner(UUID owner) {
        this.owner = owner;
    }
    
    public String getGroupName() {
        return groupName;
    }
    
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    public boolean isForRent() {
        return forRent;
    }
    
    public void setForRent(boolean forRent) {
        this.forRent = forRent;
    }
    
    public double getRentPrice() {
        return rentPrice;
    }
    
    public void setRentPrice(double rentPrice) {
        this.rentPrice = rentPrice;
    }
    
    public int getMaxRentDays() {
        return maxRentDays;
    }
    
    public void setMaxRentDays(int maxRentDays) {
        this.maxRentDays = maxRentDays;
    }
    
    public int getDefaultRentDays() {
        return defaultRentDays;
    }
    
    public void setDefaultRentDays(int defaultRentDays) {
        this.defaultRentDays = defaultRentDays;
    }
    
    public World getWorld() {
        return min.getWorld();
    }
    
    public double getCenterX() {
        return (min.getX() + max.getX()) / 2;
    }
    
    public double getCenterY() {
        return (min.getY() + max.getY()) / 2;
    }
    
    public double getCenterZ() {
        return (min.getZ() + max.getZ()) / 2;
    }
    
    public Location getCenter() {
        return new Location(getWorld(), getCenterX(), getCenterY(), getCenterZ());
    }
    
    public boolean contains(Location location) {
        if (!location.getWorld().equals(getWorld())) {
            return false;
        }
        
        double x = location.getX();
        double y = location.getY();
        double z = location.getZ();
        
        return x >= Math.min(min.getX(), max.getX()) && 
               x <= Math.max(min.getX(), max.getX()) &&
               y >= Math.min(min.getY(), max.getY()) && 
               y <= Math.max(min.getY(), max.getY()) &&
               z >= Math.min(min.getZ(), max.getZ()) && 
               z <= Math.max(min.getZ(), max.getZ());
    }
    
    public boolean contains(Player player) {
        return contains(player.getLocation());
    }
    
    public boolean isOwner(Player player) {
        return player.getUniqueId().equals(owner);
    }
    
    public boolean isOwner(UUID uuid) {
        return uuid.equals(owner);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Cell cell = (Cell) obj;
        return name.equals(cell.name);
    }
    
    @Override
    public int hashCode() {
        return name.hashCode();
    }
    
    @Override
    public String toString() {
        return "Cell{name='" + name + "', owner=" + owner + ", group='" + groupName + "', world=" + getWorld().getName() + "}";
    }
}