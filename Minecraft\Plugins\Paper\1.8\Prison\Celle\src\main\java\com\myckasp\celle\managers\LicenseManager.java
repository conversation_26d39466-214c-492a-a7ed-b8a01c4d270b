package com.myckasp.celle.managers;

import com.myckasp.celle.Celle;
import com.myckasp.celle.utils.LicenseValidator;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.scheduler.BukkitTask;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;

/**
 * Manages license validation and enforcement for the CelleSystem plugin
 */
public class LicenseManager {
    
    private final Celle plugin;
    private final LicenseValidator validator;
    
    private boolean licenseValid = false;
    private boolean licenseChecked = false;
    private String lastValidationMessage = "";
    private String licenseKey = "";
    private BukkitTask periodicValidationTask;
    
    public LicenseManager(Celle plugin) {
        this.plugin = plugin;
        this.validator = new LicenseValidator(plugin);
        this.licenseKey = plugin.getConfigManager().getString("license.key", "YOUR_LICENSE_KEY_HERE");
    }
    
    /**
     * Initializes the license system and performs initial validation
     * @return CompletableFuture that completes when license validation is done
     */
    public CompletableFuture<Boolean> initialize() {
        plugin.getLogger().info("Initializing license system...");
        
        // Check if validation on startup is enabled
        boolean validateOnStartup = plugin.getConfigManager().getBoolean("license.validation.validate-on-startup", true);
        
        if (!validateOnStartup) {
            plugin.getLogger().info("License validation on startup is disabled. Plugin will run without validation.");
            licenseValid = true;
            licenseChecked = true;
            return CompletableFuture.completedFuture(true);
        }
        
        return validateLicenseAsync().thenApply(result -> {
            if (result.isValid()) {
                plugin.getLogger().info("License validation successful! Plugin is licensed to run.");
                licenseValid = true;
                startPeriodicValidation();
            } else {
                plugin.getLogger().severe("License validation failed: " + result.getMessage());
                plugin.getLogger().severe("Plugin functionality will be disabled!");
                licenseValid = false;
                
                // Notify online operators about license issue
                notifyOperators("§c[CelleSystem] License validation failed: " + result.getMessage());
                notifyOperators("§c[CelleSystem] Plugin functionality has been disabled!");
            }
            
            licenseChecked = true;
            lastValidationMessage = result.getMessage();
            return licenseValid;
        }).exceptionally(throwable -> {
            plugin.getLogger().log(Level.SEVERE, "Failed to validate license during initialization", throwable);
            licenseValid = false;
            licenseChecked = true;
            lastValidationMessage = "Validation error: " + throwable.getMessage();
            return false;
        });
    }
    
    /**
     * Validates the license asynchronously
     * @return CompletableFuture containing the validation result
     */
    public CompletableFuture<LicenseValidator.LicenseValidationResult> validateLicenseAsync() {
        return validator.validateLicenseAsync(licenseKey);
    }
    
    /**
     * Validates the license synchronously
     * @return LicenseValidationResult containing validation results
     */
    public LicenseValidator.LicenseValidationResult validateLicense() {
        return validator.validateLicense(licenseKey);
    }
    
    /**
     * Starts periodic license validation if enabled
     */
    private void startPeriodicValidation() {
        boolean periodicValidation = plugin.getConfigManager().getBoolean("license.validation.periodic-validation", true);
        
        if (!periodicValidation) {
            return;
        }
        
        int intervalHours = plugin.getConfigManager().getInt("license.validation.validation-interval", 24);
        long intervalTicks = intervalHours * 60 * 60 * 20L; // Convert hours to ticks
        
        periodicValidationTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            plugin.getLogger().info("Performing periodic license validation...");
            
            LicenseValidator.LicenseValidationResult result = validateLicense();
            
            if (!result.isValid()) {
                plugin.getLogger().severe("Periodic license validation failed: " + result.getMessage());
                licenseValid = false;
                lastValidationMessage = result.getMessage();
                
                // Notify operators about license issue
                Bukkit.getScheduler().runTask(plugin, () -> {
                    notifyOperators("§c[CelleSystem] License validation failed: " + result.getMessage());
                    notifyOperators("§c[CelleSystem] Plugin functionality has been disabled!");
                });
            } else {
                plugin.getLogger().info("Periodic license validation successful.");
                licenseValid = true;
                lastValidationMessage = result.getMessage();
            }
            
        }, intervalTicks, intervalTicks);
        
        plugin.getLogger().info("Periodic license validation started (interval: " + intervalHours + " hours)");
    }
    
    /**
     * Stops periodic license validation
     */
    public void stopPeriodicValidation() {
        if (periodicValidationTask != null) {
            periodicValidationTask.cancel();
            periodicValidationTask = null;
            plugin.getLogger().info("Periodic license validation stopped.");
        }
    }
    
    /**
     * Checks if the plugin is licensed to run
     * @return true if license is valid, false otherwise
     */
    public boolean isLicenseValid() {
        return licenseValid;
    }
    
    /**
     * Checks if license validation has been completed
     * @return true if license has been checked, false otherwise
     */
    public boolean isLicenseChecked() {
        return licenseChecked;
    }
    
    /**
     * Gets the last validation message
     * @return the last validation message
     */
    public String getLastValidationMessage() {
        return lastValidationMessage;
    }
    
    /**
     * Gets the current license key
     * @return the license key (masked for security)
     */
    public String getLicenseKey() {
        if (licenseKey == null || licenseKey.isEmpty() || "YOUR_LICENSE_KEY_HERE".equals(licenseKey)) {
            return "Not configured";
        }
        
        // Mask the license key for security
        if (licenseKey.length() > 8) {
            return licenseKey.substring(0, 4) + "****" + licenseKey.substring(licenseKey.length() - 4);
        } else {
            return "****";
        }
    }
    
    /**
     * Reloads the license configuration and revalidates
     * @return CompletableFuture that completes when revalidation is done
     */
    public CompletableFuture<Boolean> reloadLicense() {
        // Stop current periodic validation
        stopPeriodicValidation();
        
        // Reload license key from config
        licenseKey = plugin.getConfigManager().getString("license.key", "YOUR_LICENSE_KEY_HERE");
        
        // Reset state
        licenseValid = false;
        licenseChecked = false;
        lastValidationMessage = "";
        
        // Reinitialize
        return initialize();
    }
    
    /**
     * Notifies all online operators about a message
     * @param message the message to send
     */
    private void notifyOperators(String message) {
        Bukkit.getOnlinePlayers().forEach(player -> {
            if (player.isOp()) {
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
            }
        });
    }
    
    /**
     * Shuts down the license manager
     */
    public void shutdown() {
        stopPeriodicValidation();
    }
}
