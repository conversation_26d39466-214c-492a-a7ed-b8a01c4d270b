package com.myckasp.celle.listeners;

import com.myckasp.celle.Celle;
import com.myckasp.celle.commands.AdminCommand;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.models.CellGroup;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class AdminChatListener implements Listener {
    
    private final Celle plugin;
    private final CellManager cellManager;
    private final AdminCommand adminCommand;
    
    // Store pending inputs and last opened GUI
    private final Map<UUID, PendingInput> pendingInputs = new HashMap<>();
    private final Map<UUID, String> lastOpenedGUI = new HashMap<>();
    
    public enum InputType {
        MAX_RENT_DAYS,
        MAX_MEMBERS,
        PRICE,
        REMOVE_GROUP_CONFIRM,
        NEW_GROUP_NAME
    }
    
    public static class PendingInput {
        public final InputType type;
        public final String groupName;
        
        public PendingInput(InputType type, String groupName) {
            this.type = type;
            this.groupName = groupName;
        }
    }
    
    public AdminChatListener(Celle plugin, AdminCommand adminCommand) {
        this.plugin = plugin;
        this.cellManager = plugin.getCellManager();
        this.adminCommand = adminCommand;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        // Check if player has a pending input
        if (pendingInputs.containsKey(playerId)) {
            // Cancel the event to prevent the message from being sent to chat
            event.setCancelled(true);
            
            // Remove the pending input immediately to prevent processing multiple times
            PendingInput pendingInput = pendingInputs.remove(playerId);
            
            String message = event.getMessage().trim();
            
            try {
                switch (pendingInput.type) {
                    case MAX_RENT_DAYS:
                        handleMaxRentDaysInput(player, pendingInput.groupName, message);
                        break;
                    case MAX_MEMBERS:
                        handleMaxMembersInput(player, pendingInput.groupName, message);
                        break;
                    case PRICE:
                        handlePriceInput(player, pendingInput.groupName, message);
                        break;
                    case REMOVE_GROUP_CONFIRM:
                        handleRemoveGroupConfirm(player, pendingInput.groupName, message);
                        break;
                    case NEW_GROUP_NAME:
                        handleNewGroupNameInput(player, message);
                        break;
                }
            } catch (NumberFormatException e) {
                player.sendMessage("§cUgyldigt tal! Prøv igen.");
            } catch (Exception e) {
                player.sendMessage("§cFejl: " + e.getMessage());
            }
            
            // Reopen the last GUI after processing the input
            reopenLastGUI(player);
        }
    }
    
    public void requestMaxRentDaysInput(Player player, String groupName) {
        // Close the current GUI
        player.closeInventory();
        
        // Store the current GUI title so we can reopen it later
        lastOpenedGUI.put(player.getUniqueId(), "§6Administration: " + groupName);
        pendingInputs.put(player.getUniqueId(), new PendingInput(InputType.MAX_RENT_DAYS, groupName));
        player.sendMessage("§eSkriv den nye maks lejeperiode i chatten:");
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.cancelled-message"));
    }
    
    public void requestMaxMembersInput(Player player, String groupName) {
        // Close the current GUI
        player.closeInventory();
        
        // Store the current GUI title so we can reopen it later
        lastOpenedGUI.put(player.getUniqueId(), "§6Administration: " + groupName);
        pendingInputs.put(player.getUniqueId(), new PendingInput(InputType.MAX_MEMBERS, groupName));
        player.sendMessage("§eSkriv det nye maks antal medlemmer i chatten:");
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.cancelled-message"));
    }
    
    public void requestPriceInput(Player player, String groupName) {
        // Close the current GUI
        player.closeInventory();
        
        // Store the current GUI title so we can reopen it later
        lastOpenedGUI.put(player.getUniqueId(), "§6Administration: " + groupName);
        pendingInputs.put(player.getUniqueId(), new PendingInput(InputType.PRICE, groupName));
        player.sendMessage("§eSkriv den nye lejepris i chatten:");
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.cancelled-message"));
    }
    
    public void requestRemoveGroupConfirm(Player player, String groupName) {
        // Close the current GUI
        player.closeInventory();
        
        // Store the current GUI title so we can reopen it later
        lastOpenedGUI.put(player.getUniqueId(), "§6Administration: " + groupName);
        pendingInputs.put(player.getUniqueId(), new PendingInput(InputType.REMOVE_GROUP_CONFIRM, groupName));
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.confirm-required", groupName));
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.confirm-instruction"));
    }
    
    public void requestNewGroupNameInput(Player player) {
        // Close the current GUI
        player.closeInventory();
        
        // Store the current GUI title so we can reopen it later
        lastOpenedGUI.put(player.getUniqueId(), "§6Grupper Administration");
        pendingInputs.put(player.getUniqueId(), new PendingInput(InputType.NEW_GROUP_NAME, ""));
        player.sendMessage("§eSkriv navnet på den nye gruppe i chatten:");
        player.sendMessage(plugin.getLanguageManager().getMessage("admin.cancelled-message"));
    }
    
    private void handleMaxRentDaysInput(Player player, String groupName, String input) {
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage("§7Annulleret.");
            return;
        }
        
        int maxRentDays = Integer.parseInt(input);
        
        if (maxRentDays < 1) {
            player.sendMessage(plugin.getLanguageManager().getMessage("admin.number-too-small", "1"));
            return;
        }
        
        if (maxRentDays > 365) {
            player.sendMessage(plugin.getLanguageManager().getMessage("admin.number-too-large", "365"));
            return;
        }
        
        CellGroup group = cellManager.getGroup(groupName);
        if (group != null) {
            group.setMaxRentDays(maxRentDays);
            cellManager.saveGroup(group);
            player.sendMessage("§aMaks lejeperiode for gruppen '" + groupName + "' er nu sat til " + maxRentDays + " dage.");
        } else {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
        }
    }
    
    private void handleMaxMembersInput(Player player, String groupName, String input) {
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage(plugin.getLanguageManager().getMessage("admin.cancelled"));
            return;
        }
        
        int maxMembers = Integer.parseInt(input);
        
        if (maxMembers < 1) {
            player.sendMessage("§cMaks antal medlemmer skal være mindst 1!");
            return;
        }
        
        if (maxMembers > 10) {
            player.sendMessage("§cMaks antal medlemmer kan ikke være mere end 10!");
            return;
        }
        
        CellGroup group = cellManager.getGroup(groupName);
        if (group != null) {
            group.setMaxMembers(maxMembers);
            cellManager.saveGroup(group);
            player.sendMessage("§aMaks antal medlemmer for gruppen '" + groupName + "' er nu sat til " + maxMembers + ".");
        } else {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
        }
    }
    
    private void handlePriceInput(Player player, String groupName, String input) {
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage(plugin.getLanguageManager().getMessage("admin.cancelled"));
            return;
        }
        
        double price = Double.parseDouble(input);
        
        if (price < 0) {
            player.sendMessage("§cPrisen kan ikke være negativ!");
            return;
        }
        
        if (price > 10000) {
            player.sendMessage("§cPrisen kan ikke være mere end $10,000!");
            return;
        }
        
        CellGroup group = cellManager.getGroup(groupName);
        if (group != null) {
            group.setDefaultRentPrice(price);
            cellManager.saveGroup(group);
            player.sendMessage("§aLejepris for gruppen '" + groupName + "' er nu sat til $" + (int) price + "/dag.");
        } else {
            player.sendMessage("§cGruppen '" + groupName + "' blev ikke fundet!");
        }
    }
    
    private void handleRemoveGroupConfirm(Player player, String groupName, String input) {
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage("§7Sletning annulleret.");
            return;
        }
        
        if (!input.equalsIgnoreCase("confirm")) {
            player.sendMessage("§cUgyldig bekræftelse! Skriv 'confirm' for at slette eller 'cancel' for at annullere.");
            return;
        }
        
        // Check if it's the default group
        if ("default".equalsIgnoreCase(groupName)) {
            player.sendMessage("§cDu kan ikke slette standardgruppen!");
            return;
        }
        
        // Check if group exists
        if (!cellManager.groupExists(groupName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("admin.group-not-found", groupName));
            return;
        }
        
        // Remove group
        cellManager.removeGroup(groupName);
        player.sendMessage("§aGruppen '" + groupName + "' er blevet slettet.");
    }
    
    private void handleNewGroupNameInput(Player player, String input) {
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage("§7Oprettelse annulleret.");
            // Reopen the group list GUI when cancelled
            player.closeInventory(); // Close any open inventory first
            adminCommand.showGroupList(player);
            return;
        }
        
        // Validate group name
        if (input.trim().isEmpty()) {
            player.sendMessage("§cGruppenavn kan ikke være tomt!");
            return;
        }
        
        // Check if group name contains only valid characters
        if (!input.matches("^[a-zA-Z0-9_æøåÆØÅ]+$")) {
            player.sendMessage("§cGruppenavn kan kun indeholde bogstaver, tal og underscore!");
            return;
        }
        
        // Check if group already exists
        if (cellManager.groupExists(input)) {
            player.sendMessage("§cEn gruppe med navnet '" + input + "' eksisterer allerede!");
            return;
        }
        
        // Create new group
        CellGroup newGroup = new CellGroup(input, "Ny gruppe oprettet af admin");
        
        // Add group to the in-memory map and save it
        cellManager.getGroups().put(input, newGroup);
        cellManager.saveGroup(newGroup);
        player.sendMessage("§aGruppen '" + input + "' er blevet oprettet med standardindstillinger.");
        
        // Close any open inventory first, then reopen the group list GUI
        player.closeInventory();
        adminCommand.showGroupList(player);
    }
    
    /**
     * Reopens the last GUI that was open before chat input was requested
     * @param player The player whose GUI should be reopened
     */
    private void reopenLastGUI(Player player) {
        UUID playerId = player.getUniqueId();
        String guiTitle = lastOpenedGUI.get(playerId);
        
        // Close any open inventory first to prevent interaction issues
        player.closeInventory();
        
        if (guiTitle != null) {
            // Remove the stored GUI title
            lastOpenedGUI.remove(playerId);
            
            // Reopen the appropriate GUI based on the stored title
            if (guiTitle.equals("§6Grupper Administration")) {
                adminCommand.showGroupList(player);
            } else if (guiTitle.startsWith("§6Administration: ")) {
                String groupName = guiTitle.replace("§6Administration: ", "");
                adminCommand.showGroupManagement(player, groupName);
            }
        }
    }
}