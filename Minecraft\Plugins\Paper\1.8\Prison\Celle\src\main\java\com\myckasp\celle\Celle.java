package com.myckasp.celle;

import com.myckasp.celle.commands.CelleCommand;
import com.myckasp.celle.commands.AdminCommand;
import com.myckasp.celle.config.ConfigManager;
import com.myckasp.celle.config.LanguageManager;
import com.myckasp.celle.listeners.SignInteractListener;
import com.myckasp.celle.listeners.AdminChatListener;
import com.myckasp.celle.listeners.AvailableCellsSignListener;
import com.myckasp.celle.listeners.PlayerJoinListener;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.managers.LicenseManager;
import com.myckasp.celle.managers.UpdateManager;
import com.myckasp.celle.utils.WorldGuardUtils;
import org.bukkit.plugin.java.JavaPlugin;

public class Celle extends JavaPlugin {
    
    private static Celle instance;
    private ConfigManager configManager;
    private LanguageManager languageManager;
    private CellManager cellManager;
    private LicenseManager licenseManager;
    private UpdateManager updateManager;
    private WorldGuardUtils worldGuardUtils;
    private AdminCommand adminCommand;
    private AdminChatListener adminChatListener;
    private AvailableCellsSignListener availableCellsSignListener;
    private PlayerJoinListener playerJoinListener;
    
    @Override
    public void onEnable() {
        instance = this;

        // Initialize basic managers first
        configManager = new ConfigManager(this);
        languageManager = new LanguageManager(this);

        // Initialize license manager and validate license
        licenseManager = new LicenseManager(this);

        getLogger().info("Initializing CelleSystem plugin...");

        // Validate license before continuing with plugin initialization
        licenseManager.initialize().thenAccept(licenseValid -> {
            if (licenseValid) {
                // License is valid, continue with full plugin initialization
                getServer().getScheduler().runTask(this, this::initializePlugin);
            } else {
                // License is invalid, initialize in limited mode
                getLogger().severe("Plugin is running in limited mode due to license validation failure!");
                getLogger().severe("Please configure a valid license key in config.yml");
                getLogger().severe("Get your license from: https://myckasplugins.flyfiles.dk");

                // Still initialize basic components but disable main functionality
                initializeLimitedMode();
            }
        }).exceptionally(throwable -> {
            getLogger().severe("Failed to validate license: " + throwable.getMessage());
            getLogger().severe("Plugin will run in limited mode!");
            initializeLimitedMode();
            return null;
        });
    }

    /**
     * Initialize the plugin with full functionality (when license is valid)
     */
    private void initializePlugin() {
        getLogger().info("License validated successfully! Initializing full plugin functionality...");

        // Initialize remaining managers
        cellManager = new CellManager(this);
        updateManager = new UpdateManager(this);
        worldGuardUtils = new WorldGuardUtils(this);

        // Register commands
        getCommand("celle").setExecutor(new CelleCommand(this));

        // Register admin command
        adminCommand = new AdminCommand(this);
        getCommand("ce").setExecutor(adminCommand);

        // Register events
        getServer().getPluginManager().registerEvents(new SignInteractListener(this), this);
        adminChatListener = new AdminChatListener(this, adminCommand);
        availableCellsSignListener = new AvailableCellsSignListener(this);
        playerJoinListener = new PlayerJoinListener(this);

        getLogger().info("CelleSystem plugin er blevet aktiveret!");

        // Check for updates after all managers are initialized
        getServer().getScheduler().runTaskLater(this, () -> {
            updateManager.checkForUpdatesAsync();
        }, 20L); // Delay by 1 second (20 ticks)
    }

    /**
     * Initialize the plugin in limited mode (when license is invalid)
     */
    private void initializeLimitedMode() {
        getLogger().info("Initializing plugin in limited mode...");

        // Only initialize essential components for license management
        // Don't initialize cell management, commands, or event listeners

        getLogger().warning("CelleSystem plugin is running in limited mode due to license issues!");
        getLogger().warning("Please configure a valid license key to enable full functionality.");
    }
    
    @Override
    public void onDisable() {
        // Shutdown license manager
        if (licenseManager != null) {
            licenseManager.shutdown();
        }

        // Shutdown update manager
        if (updateManager != null) {
            updateManager.shutdown();
        }

        getLogger().info("CelleSystem plugin er blevet deaktiveret!");
    }
    
    public static Celle getInstance() {
        return instance;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public LanguageManager getLanguageManager() {
        return languageManager;
    }
    
    public CellManager getCellManager() {
        return cellManager;
    }

    public LicenseManager getLicenseManager() {
        return licenseManager;
    }

    public UpdateManager getUpdateManager() {
        return updateManager;
    }

    public WorldGuardUtils getWorldGuardUtils() {
        return worldGuardUtils;
    }

    /**
     * Checks if the plugin is licensed and functional
     * @return true if license is valid and plugin is fully functional
     */
    public boolean isLicensed() {
        return licenseManager != null && licenseManager.isLicenseValid();
    }
    
    public AdminCommand getAdminCommand() {
        return adminCommand;
    }
    
    public AdminChatListener getAdminChatListener() {
        return adminChatListener;
    }
    
    public AvailableCellsSignListener getAvailableCellsSignListener() {
        return availableCellsSignListener;
    }
    
    public PlayerJoinListener getPlayerJoinListener() {
        return playerJoinListener;
    }
}