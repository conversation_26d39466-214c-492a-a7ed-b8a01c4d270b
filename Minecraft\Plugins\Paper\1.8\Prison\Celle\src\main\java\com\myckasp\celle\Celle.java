package com.myckasp.celle;

import com.myckasp.celle.commands.CelleCommand;
import com.myckasp.celle.commands.AdminCommand;
import com.myckasp.celle.config.ConfigManager;
import com.myckasp.celle.config.LanguageManager;
import com.myckasp.celle.listeners.SignInteractListener;
import com.myckasp.celle.listeners.AdminChatListener;
import com.myckasp.celle.listeners.AvailableCellsSignListener;
import com.myckasp.celle.listeners.PlayerJoinListener;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.managers.UpdateManager;
import com.myckasp.celle.utils.WorldGuardUtils;
import org.bukkit.plugin.java.JavaPlugin;

public class Celle extends JavaPlugin {
    
    private static Celle instance;
    private ConfigManager configManager;
    private LanguageManager languageManager;
    private CellManager cellManager;
    private UpdateManager updateManager;
    private WorldGuardUtils worldGuardUtils;
    private AdminCommand adminCommand;
    private AdminChatListener adminChatListener;
    private AvailableCellsSignListener availableCellsSignListener;
    private PlayerJoinListener playerJoinListener;
    
    @Override
    public void onEnable() {
        instance = this;
        
        // Initialize managers
        configManager = new ConfigManager(this);
        languageManager = new LanguageManager(this);
        cellManager = new CellManager(this);
        updateManager = new UpdateManager(this);
        worldGuardUtils = new WorldGuardUtils(this);
        
        // Register commands
        getCommand("celle").setExecutor(new CelleCommand(this));
        
        // Register admin command
        adminCommand = new AdminCommand(this);
        getCommand("ce").setExecutor(adminCommand);
        
        // Register events
        getServer().getPluginManager().registerEvents(new SignInteractListener(this), this);
        adminChatListener = new AdminChatListener(this, adminCommand);
        availableCellsSignListener = new AvailableCellsSignListener(this);
        playerJoinListener = new PlayerJoinListener(this);
        
        getLogger().info("CelleSystem plugin er blevet aktiveret!");
        
        // Check for updates after all managers are initialized
        getServer().getScheduler().runTaskLater(this, () -> {
            updateManager.checkForUpdatesAsync();
        }, 20L); // Delay by 1 second (20 ticks)
    }
    
    @Override
    public void onDisable() {
        // Shutdown update manager
        if (updateManager != null) {
            updateManager.shutdown();
        }
        
        getLogger().info("CelleSystem plugin er blevet deaktiveret!");
    }
    
    public static Celle getInstance() {
        return instance;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public LanguageManager getLanguageManager() {
        return languageManager;
    }
    
    public CellManager getCellManager() {
        return cellManager;
    }
    
    public UpdateManager getUpdateManager() {
        return updateManager;
    }
    
    public WorldGuardUtils getWorldGuardUtils() {
        return worldGuardUtils;
    }
    
    public AdminCommand getAdminCommand() {
        return adminCommand;
    }
    
    public AdminChatListener getAdminChatListener() {
        return adminChatListener;
    }
    
    public AvailableCellsSignListener getAvailableCellsSignListener() {
        return availableCellsSignListener;
    }
    
    public PlayerJoinListener getPlayerJoinListener() {
        return playerJoinListener;
    }
}