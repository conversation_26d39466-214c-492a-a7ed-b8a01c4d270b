package com.myckasp.celle.models;

public class CellGroup {
    
    private String name;
    private String description;
    private double defaultRentPrice;
    private int maxRentDays;
    private int maxMembers;
    
    public CellGroup(String name, String description) {
        this.name = name;
        this.description = description;
        // Set default price based on group name
        if ("default".equalsIgnoreCase(name)) {
            this.defaultRentPrice = 350.0; // Default price for default group
            this.maxRentDays = 10; // Default max rent days
            this.maxMembers = 1; // Default max members
        } else {
            this.defaultRentPrice = 350.0; // Default for other groups too for now
            this.maxRentDays = 10; // Default max rent days
            this.maxMembers = 1; // Default max members
        }
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public double getDefaultRentPrice() {
        return defaultRentPrice;
    }
    
    public void setDefaultRentPrice(double defaultRentPrice) {
        this.defaultRentPrice = defaultRentPrice;
    }
    
    public int getMaxRentDays() {
        return maxRentDays;
    }
    
    public void setMaxRentDays(int maxRentDays) {
        this.maxRentDays = maxRentDays;
    }
    
    public int getMaxMembers() {
        return maxMembers;
    }
    
    public void setMaxMembers(int maxMembers) {
        this.maxMembers = maxMembers;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        CellGroup cellGroup = (CellGroup) obj;
        return name.equals(cellGroup.name);
    }
    
    @Override
    public int hashCode() {
        return name.hashCode();
    }
    
    @Override
    public String toString() {
        return "CellGroup{name='" + name + "', description='" + description + "', maxRentDays=" + maxRentDays + ", maxMembers=" + maxMembers + "}";
    }
}