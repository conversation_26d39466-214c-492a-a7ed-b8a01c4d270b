package com.myckasp.celle.listeners;

import com.myckasp.celle.Celle;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.models.Cell;
import org.bukkit.block.Sign;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;

public class SignInteractListener implements Listener {
    
    private final Celle plugin;
    
    public SignInteractListener(Celle plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        if (!(event.getClickedBlock().getState() instanceof Sign)) {
            return;
        }
        
        Sign sign = (Sign) event.getClickedBlock().getState();
        Player player = event.getPlayer();
        
        // Check if first line is "Leje"
        if (!sign.getLine(0).equalsIgnoreCase("Leje")) {
            return;
        }
        
        // Get cell name from second line
        String cellName = sign.getLine(1);
        if (cellName == null || cellName.isEmpty()) {
            return;
        }
        
        CellManager cellManager = plugin.getCellManager();
        
        // Check if cell exists
        if (!cellManager.cellExists(cellName)) {
            player.sendMessage("§cCellen '" + cellName + "' blev ikke fundet!");
            return;
        }
        
        Cell cell = cellManager.getCell(cellName);
        
        // Check if cell is for rent
        if (!cell.isForRent()) {
            player.sendMessage(plugin.getLanguageManager().getMessage("rental.cell-not-for-rent"));
            return;
        }
        
        // Check if player is already the owner
        if (cell.isOwner(player)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("rental.cell-already-owned"));
            return;
        }
        
        // Get price per day from third line
        String priceText = sign.getLine(2);
        double pricePerDay = 350.0; // Default price
        
        try {
            // Extract numeric value from price text (e.g., "350 kr./dag" -> 350)
            String priceStr = priceText.replaceAll("[^0-9.]", "");
            if (!priceStr.isEmpty()) {
                pricePerDay = Double.parseDouble(priceStr);
            }
        } catch (NumberFormatException e) {
            plugin.getLogger().warning("Ugyldig prisformat på skilt for cellen '" + cellName + "'");
        }
        
        // Get max days from fourth line
        String maxDaysText = sign.getLine(3);
        int maxDays = 10; // Default max days
        
        try {
            // Extract numeric value from max days text (e.g., "Max 10 dage" -> 10)
            String daysStr = maxDaysText.replaceAll("[^0-9]", "");
            if (!daysStr.isEmpty()) {
                maxDays = Integer.parseInt(daysStr);
            }
        } catch (NumberFormatException e) {
            plugin.getLogger().warning("Ugyldigt dageformat på skilt for cellen '" + cellName + "'");
        }
        
        // Calculate default rental cost (1 day)
        double totalCost = pricePerDay;
        
        // Check if player has enough money (would need economy plugin integration)
        // For now, just proceed with the rental
        
        // Set the player as the renter (for now, just setting owner)
        // In a real implementation, this would be a separate renter field
        cell.setOwner(player.getUniqueId());
        cellManager.saveCell(cell);
        
        player.sendMessage(plugin.getLanguageManager().getMessage("rental.cell-rented", cellName, "1", String.valueOf(totalCost)));
        player.sendMessage("§aDu kan forlænge lejen ved at bruge /celle rent " + cellName + " [dage]");
    }
}