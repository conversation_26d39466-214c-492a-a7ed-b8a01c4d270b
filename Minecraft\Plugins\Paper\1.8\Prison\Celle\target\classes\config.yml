# CelleSystem Plugin Configuration
# Default configuration file for the CelleSystem prison cell management plugin

# General settings
general:
  # Debug mode (true/false)
  debug: false
  
  # Language file to use
  language: 'da_dk.yml'
  
  # Auto-save interval in minutes (0 to disable)
  auto-save: 5

# Cell settings
cells:
  # Default cell size
  default-size: 10
  
  # Default cell height
  default-height: 8
  
  # Whether to create cells automatically when a player claims one
  auto-create: true
  
  # Maximum number of cells a player can own
  max-cells-per-player: 5
  
  # Whether to allow cell name changes
  allow-name-changes: true
  
  # Whether to allow cell description changes
  allow-description-changes: true
  
  # Rental settings
  rental:
    # Whether rental functionality is enabled
    enabled: true
    
    # Default rent price for cells (per day)
    default-price: 350.0
    
    # Maximum number of days a player can rent a cell for
    max-days: 10
    
    # Default number of days for a rental
    default-days: 1
    
    # Whether players can rent cells they already own
    allow-rent-owned: false

# WorldGuard integration
worldguard:
  # Whether WorldGuard is required
  required: true
  
  # Default region flags for cells
  default-flags:
    # Build permissions
    build: true
    
    # Container permissions
    container: true
    
    # Door permissions
    door: true
    
    # Switch permissions
    switch: true
    
    # Mob damage permissions
    mob-damage: false
    
    # PvP permissions
    pvp: false
    
    # Block break permissions
    block-break: false
    
    # Block place permissions
    block-place: true

# Economy settings (if using an economy plugin)
economy:
  # Whether to use economy features
  enabled: false
  
  # Cost to claim a cell
  claim-cost: 100.0
  
  # Daily rent for cells
  daily-rent: 10.0
  
  # Whether to send rent reminders
  rent-reminders: true
  
  # Time before rent is due (in hours)
  rent-reminder-time: 24

# Message settings
messages:
  # Prefix for plugin messages
  prefix: '&8[&6CelleSystem&8]'
  
  # Whether to use color codes in messages
  colors: true
  
  # Console prefix
  console-prefix: '[CelleSystem]'

# License settings
license:
  # Your license key for the CelleSystem plugin
  # Get your license from: https://myckasplugins.flyfiles.dk
  # Paste your license key below (e.g., CELLS-ABC123DEF456)
  key: 'YOUR_LICENSE_KEY_HERE'

  # License validation settings
  validation:
    # Whether to validate license on startup (recommended: true)
    validate-on-startup: true

    # Whether to periodically revalidate license (recommended: true)
    periodic-validation: true

    # How often to revalidate license in hours (default: 24)
    validation-interval: 24

    # Connection timeout for license validation in seconds
    timeout: 10

# Database settings
database:
  # Database type (file/mysql)
  type: 'file'

  # MySQL settings (if using MySQL)
  mysql:
    host: 'localhost'
    port: 3306
    database: 'celle'
    username: 'root'
    password: ''

    # Table prefix
    table-prefix: 'celle_'

    # Connection pool settings
    pool:
      max-size: 10
      idle-timeout: 600000
      connection-timeout: 5000