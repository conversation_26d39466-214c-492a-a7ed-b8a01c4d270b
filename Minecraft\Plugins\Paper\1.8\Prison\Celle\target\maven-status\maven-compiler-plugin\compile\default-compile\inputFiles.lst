C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\Celle.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\config\LanguageManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\managers\CellManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\commands\CelleCommand.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\utils\UpdateChecker.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\config\ConfigManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\listeners\AvailableCellsSignListener.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\commands\AdminCommand.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\utils\WorldGuardUtils.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\models\Cell.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\listeners\PlayerJoinListener.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\listeners\AdminChatListener.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\managers\UpdateManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\models\CellGroup.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\1.8\Prison\Celle\src\main\java\com\myckasp\celle\listeners\SignInteractListener.java
