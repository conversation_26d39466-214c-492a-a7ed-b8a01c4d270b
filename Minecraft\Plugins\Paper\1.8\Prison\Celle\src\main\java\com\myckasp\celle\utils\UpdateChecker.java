package com.myckasp.celle.utils;

import com.myckasp.celle.Celle;
import org.bukkit.plugin.Plugin;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class UpdateChecker {
    
    private static final String GITHUB_API_URL = "https://api.github.com/repos/MyckasP/CelleSystem-Latest/releases/latest";
    private static final String RAW_JSON_URL = "https://raw.githubusercontent.com/MyckasP/CelleSystem-Latest/refs/heads/main/latest.json";
    private final Plugin plugin;
    private final ExecutorService executorService;
    
    public UpdateChecker(Plugin plugin) {
        this.plugin = plugin;
        this.executorService = Executors.newSingleThreadExecutor();
    }
    
    public CompletableFuture<Boolean> checkForUpdates() {
        return CompletableFuture.supplyAsync(() -> {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(RAW_JSON_URL);
                HttpResponse response = httpClient.execute(request);
                
                if (response.getStatusLine().getStatusCode() == 200) {
                    String json = EntityUtils.toString(response.getEntity());
                    // Simple JSON parsing - extract the value after the first colon
                    String latestVersion = extractJsonStringValue(json, "latest");
                    String currentVersion = plugin.getDescription().getVersion();
                    
                    // If we can't parse the latest version, assume no update is available
                    if (latestVersion == null) {
                        plugin.getLogger().warning("Could not parse latest version from JSON - update check inconclusive");
                        return false;
                    }
                    
                    return !currentVersion.equals(latestVersion);
                } else {
                    plugin.getLogger().warning("Failed to check for updates. HTTP Status: " + response.getStatusLine().getStatusCode());
                }
            } catch (IOException e) {
                plugin.getLogger().warning("Could not check for updates: " + e.getMessage());
            }
            return false;
        }, executorService);
    }
    
    public String getLatestVersion() {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(RAW_JSON_URL);
            HttpResponse response = httpClient.execute(request);
            
            if (response.getStatusLine().getStatusCode() == 200) {
                String json = EntityUtils.toString(response.getEntity());
                // Simple JSON parsing - extract the value after the first colon
                return extractJsonStringValue(json, "latest");
            }
        } catch (IOException e) {
            // Silent - no need to log this as it's handled elsewhere
        }
        return null;
    }
    
    public void shutdown() {
        executorService.shutdown();
    }
    
    /**
     * Simple JSON string value extraction for basic key-value pairs
     * @param json The JSON string
     * @param key The key to extract
     * @return The value or null if not found
     */
    private String extractJsonStringValue(String json, String key) {
        try {
            // Remove newlines and extra whitespace to make it easier to parse
            String cleanedJson = json.replaceAll("\\s+", " ").trim();
            
            // Look for the pattern: "key":"value"
            String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]+)\"";
            java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = r.matcher(cleanedJson);
            
            if (m.find()) {
                return m.group(1);
            } else {
                // Let's try a different pattern - maybe the JSON is formatted differently
                pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]*?)\"\\s*,?";
                r = java.util.regex.Pattern.compile(pattern);
                m = r.matcher(cleanedJson);
                
                if (m.find()) {
                    return m.group(1);
                }
            }
        } catch (Exception e) {
            // Silent - no need to log this as it's handled elsewhere
        }
        return null;
    }
}